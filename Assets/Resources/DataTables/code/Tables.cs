
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;

namespace cfg
{
public partial class Tables
{
    public item.TbWeapon TbWeapon {get; }
    public skill.TbBuff TbBuff {get; }
    public skill.TbBuffLevel TbBuffLevel {get; }
    public skill.TbSpell TbSpell {get; }
    public skill.TbSpellLevel TbSpellLevel {get; }
    public section.TbSection TbSection {get; }
    public map.TbMap TbMap {get; }
    public role.TbMonster TbMonster {get; }
    public item.TbItems TbItems {get; }
    public bonus.TbBonus TbBonus {get; }
    public drop.TbDrop TbDrop {get; }

    public Tables(System.Func<string, ByteBuf> loader)
    {
        TbWeapon = new item.TbWeapon(loader("item_tbweapon"));
        TbBuff = new skill.TbBuff(loader("skill_tbbuff"));
        TbBuffLevel = new skill.TbBuffLevel(loader("skill_tbbufflevel"));
        TbSpell = new skill.TbSpell(loader("skill_tbspell"));
        TbSpellLevel = new skill.TbSpellLevel(loader("skill_tbspelllevel"));
        TbSection = new section.TbSection(loader("section_tbsection"));
        TbMap = new map.TbMap(loader("map_tbmap"));
        TbMonster = new role.TbMonster(loader("role_tbmonster"));
        TbItems = new item.TbItems(loader("item_tbitems"));
        TbBonus = new bonus.TbBonus(loader("bonus_tbbonus"));
        TbDrop = new drop.TbDrop(loader("drop_tbdrop"));
        ResolveRef();
    }
    
    private void ResolveRef()
    {
        TbWeapon.ResolveRef(this);
        TbBuff.ResolveRef(this);
        TbBuffLevel.ResolveRef(this);
        TbSpell.ResolveRef(this);
        TbSpellLevel.ResolveRef(this);
        TbSection.ResolveRef(this);
        TbMap.ResolveRef(this);
        TbMonster.ResolveRef(this);
        TbItems.ResolveRef(this);
        TbBonus.ResolveRef(this);
        TbDrop.ResolveRef(this);
    }
}

}
