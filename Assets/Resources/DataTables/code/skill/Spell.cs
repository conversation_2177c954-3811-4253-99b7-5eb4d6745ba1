
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class Spell : Luban.BeanBase
{
    public Spell(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Name = _buf.ReadString();
        Desc = _buf.ReadString();
        Stype = (TypeOfSkill)_buf.ReadInt();
        Quality = (Quality)_buf.ReadInt();
    }

    public static Spell DeserializeSpell(ByteBuf _buf)
    {
        return new skill.Spell(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 名称
    /// </summary>
    public readonly string Name;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Desc;
    /// <summary>
    /// 技能类型
    /// </summary>
    public readonly TypeOfSkill Stype;
    public readonly Quality Quality;
   
    public const int __ID__ = 1155860267;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "name:" + Name + ","
        + "desc:" + Desc + ","
        + "stype:" + Stype + ","
        + "quality:" + Quality + ","
        + "}";
    }
}

}

