
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class SpellLevel : Luban.BeanBase
{
    public SpellLevel(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Level = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);BaseData = new System.Collections.Generic.Dictionary<int, float>(n0 * 3 / 2);for(var i0 = 0 ; i0 < n0 ; i0++) { int _k0;  _k0 = _buf.ReadInt(); float _v0;  _v0 = _buf.ReadFloat();     BaseData.Add(_k0, _v0);}}
        RefractionRadius = _buf.ReadFloat();
    }

    public static SpellLevel DeserializeSpellLevel(ByteBuf _buf)
    {
        return new skill.SpellLevel(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 等级
    /// </summary>
    public readonly int Level;
    /// <summary>
    /// 基础伤害
    /// </summary>
    public readonly System.Collections.Generic.Dictionary<int, float> BaseData;
    /// <summary>
    /// 折射半径
    /// </summary>
    public readonly float RefractionRadius;
   
    public const int __ID__ = 1105618713;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "level:" + Level + ","
        + "baseData:" + Luban.StringUtil.CollectionToString(BaseData) + ","
        + "refractionRadius:" + RefractionRadius + ","
        + "}";
    }
}

}

