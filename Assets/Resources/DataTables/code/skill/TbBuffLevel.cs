
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public partial class TbBuffLevel
{
    private readonly System.Collections.Generic.List<skill.BuffLevel> _dataList;

    private System.Collections.Generic.Dictionary<(int, int), skill.BuffLevel> _dataMapUnion;

    public TbBuffLevel(ByteBuf _buf)
    {
        _dataList = new System.Collections.Generic.List<skill.BuffLevel>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            skill.BuffLevel _v;
            _v = skill.BuffLevel.DeserializeBuffLevel(_buf);
            _dataList.Add(_v);
        }
        _dataMapUnion = new System.Collections.Generic.Dictionary<(int, int), skill.BuffLevel>();
        foreach(var _v in _dataList)
        {
            _dataMapUnion.Add((_v.Id, _v.Level), _v);
        }
    }

    public System.Collections.Generic.List<skill.BuffLevel> DataList => _dataList;

    public skill.BuffLevel Get(int id, int level) => _dataMapUnion.TryGetValue((id, level), out skill.BuffLevel __v) ? __v : null;
    
    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }
}

}

