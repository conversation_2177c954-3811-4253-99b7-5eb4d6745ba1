
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.item
{
public sealed partial class Item : Luban.BeanBase
{
    public Item(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Name = _buf.ReadString();
        Desc = _buf.ReadString();
        Type = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);SubItem = new System.Collections.Generic.List<int>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { int _e0;  _e0 = _buf.ReadInt(); SubItem.Add(_e0);}}
        Icon = _buf.ReadString();
        PrefabPath = _buf.ReadString();
    }

    public static Item DeserializeItem(ByteBuf _buf)
    {
        return new item.Item(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 名称
    /// </summary>
    public readonly string Name;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Desc;
    /// <summary>
    /// 类型
    /// </summary>
    public readonly int Type;
    /// <summary>
    /// 箱中箱
    /// </summary>
    public readonly System.Collections.Generic.List<int> SubItem;
    /// <summary>
    /// ICON
    /// </summary>
    public readonly string Icon;
    /// <summary>
    /// 掉落物路径
    /// </summary>
    public readonly string PrefabPath;
   
    public const int __ID__ = 2107285806;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "name:" + Name + ","
        + "desc:" + Desc + ","
        + "type:" + Type + ","
        + "subItem:" + Luban.StringUtil.CollectionToString(SubItem) + ","
        + "icon:" + Icon + ","
        + "prefabPath:" + PrefabPath + ","
        + "}";
    }
}

}

