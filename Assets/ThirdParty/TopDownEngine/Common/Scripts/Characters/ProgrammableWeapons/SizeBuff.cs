using UnityEngine;

public class SizeBuff : BuffElement
{
    public float SizeMultiplier = 1.5f;

    public SizeBuff() { }
    public SizeBuff(uint id, string elementName = "")
    {
        ID = id;
        Type = ElementType.Buff;
        EffectivePoint = BuffEffectivePoint.PRECAST;
        ElementName = elementName;
    }

    public override void ApplyBuff(GameObject target)
    {
        if (target.transform != null)
        {
            target.transform.localScale += new Vector3(SizeMultiplier, SizeMultiplier, SizeMultiplier);
        }
    }
}