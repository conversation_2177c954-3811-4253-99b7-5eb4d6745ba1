using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class SkillProjectile : SkillElement
{
    [SerializeField] private float _damageMultiplier = 2f;
    private MMMultipleObjectPooler _objectPooler;
	private GameObject _owner;
	private bool _flip;
    private Dictionary<BuffEffectivePoint, List<BuffElement>> _activeBuffs;
	public int number = 1;
    private Vector3 Spread = Vector3.zero;

    public SkillProjectile(uint id, string elementName = "")
    {
        ID = id;
        Type = ElementType.Skill;
        ElementName = elementName;
        Spread = new Vector3(0, 0, 10); // test initialize
        number = 1;
    }

    public override void InitSkillProperties()
    {
        number = 1;
    }

    public override void ExecuteSkill(GameObject owner, Transform spawnPoint, List<BuffElement> activeBuffs, bool flip)
    {
        // 初始化技能属性
        InitSkillProperties();

        // 实现具体的技能逻辑
        _owner = owner;
		_flip = flip;
		ApplyBuffs(_owner, this, BuffEffectivePoint.PRECAST);
        for (int i = 0; i < number; i++)
        {
            SpawnProjectile(spawnPoint.position, i, number, true);
            PlaySpawnFeedbacks();
        }
    }

    public override void SetObjectPool(MMMultipleObjectPooler mObjectPooler)
    {
        base.SetObjectPool(mObjectPooler);
        _objectPooler = mObjectPooler; 
    }

	public override void SaveBuffs(List<BuffElement> activeBuffs)
	{ 
		// 按buff类型分组存储
		_activeBuffs = activeBuffs
			.GroupBy(b => b.EffectivePoint)  // 假设BuffElement有Type属性表示buff类型
			.ToDictionary(g => g.Key, g => g.ToList());
		
	}

	public override void ApplyBuffs(GameObject skillGameObject, SkillElement skill, BuffEffectivePoint effectpoint)
	{
        if (!_activeBuffs.ContainsKey(effectpoint)) return;
        foreach (var buff in _activeBuffs[effectpoint])
        {
            if(effectpoint == BuffEffectivePoint.PRECAST)
            {
                buff.ApplyBuff(skill);
            }
            else if (effectpoint == BuffEffectivePoint.CASTING)
            {
                buff.ApplyBuff(skillGameObject);
            }
            
        }
    }

    /// <summary>
	/// Spawns a new object and positions/resizes it
	/// </summary>
	public virtual GameObject SpawnProjectile(Vector3 spawnPosition, int projectileIndex, int totalProjectiles, bool triggerObjectActivation = true)
	{
		/// we get the next object in the pool and make sure it's not null
		GameObject nextGameObject = _objectPooler.GetPooledGameObjectOfType("SpellMissile");

		// mandatory checks
		if (nextGameObject == null) { return null; }
		if (nextGameObject.GetComponent<MMPoolableObject>() == null)
		{
			throw new Exception(_owner.name + " is trying to spawn objects that don't have a PoolableObject component.");
		}
		// we position the object
		nextGameObject.transform.position = spawnPosition;
		// we set its direction

		Projectile projectile = nextGameObject.GetComponent<Projectile>();

		// we activate the object
		nextGameObject.gameObject.SetActive(true);

		ApplyBuffs(nextGameObject, null, BuffEffectivePoint.CASTING);

		if (projectile != null)
		{
             Vector3 _randomSpreadDirection;
            if (totalProjectiles > 1)
            {
                _randomSpreadDirection.x = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.x, Spread.x);
                _randomSpreadDirection.y = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.y, Spread.y);
                _randomSpreadDirection.z = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.z, Spread.z);
            }
            else
            {
                _randomSpreadDirection = Vector3.zero;
            }
            Quaternion spread = Quaternion.Euler(_randomSpreadDirection);

            // Find the nearest enemy
            GameObject nearestEnemy = ProgrammableWeaponCommonFunctions.FindNearestCharacter(_owner.transform.position);
            Vector3 direction;
            if (nearestEnemy != null)
            {
                // Calculate direction towards the nearest enemy
                var tmp_calc_dir = nearestEnemy.transform.position + new Vector3(0, 1.0f, 0);
                direction = (tmp_calc_dir - _owner.transform.position).normalized;
            }
            else
            {
                // Default direction if no enemy is found
                direction = _flip ? Vector3.left : Vector3.right;
            }

            // Set the projectile's direction
            projectile.SetDirection(spread * _owner.transform.rotation * direction, _owner.transform.rotation, true);
        }

		if (triggerObjectActivation)
		{
			if (nextGameObject.GetComponent<MMPoolableObject>() != null)
			{
				nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
			}
		}

		return (nextGameObject);
	}

    /// <summary>
    /// This method is in charge of playing feedbacks on projectile spawn
    /// </summary>
    protected virtual void PlaySpawnFeedbacks()
    {
    }


}