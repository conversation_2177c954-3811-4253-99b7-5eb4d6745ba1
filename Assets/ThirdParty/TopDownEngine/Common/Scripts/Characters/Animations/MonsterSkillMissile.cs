using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using static UnityEngine.UI.GridLayoutGroup;
using System;
using UnityEngine;

public class MonsterSkillMissile
{
    private static Vector3 Spread = new Vector3(0, 0, 10);

    public static GameObject SpawnMissile(GameObject owner , MMObjectPooler objectPooler ,Vector3 spawnPosition, int projectileIndex, int totalProjectiles, bool triggerObjectActivation = true)
    {
        /// we get the next object in the pool and make sure it's not null
        GameObject nextGameObject = objectPooler.GetPooledGameObject();

        // mandatory checks
        if (nextGameObject == null) { return null; }
        if (nextGameObject.GetComponent<MMPoolableObject>() == null)
        {
            throw new Exception(owner.name + " is trying to spawn objects that don't have a PoolableObject component.");
        }
        // we position the object
        nextGameObject.transform.position = spawnPosition + new Vector3(0f, 1.6f, 0f); // 瞄准角色的身体
        // we set its direction

        Projectile projectile = nextGameObject.GetComponent<Projectile>();

        // we activate the object
        nextGameObject.gameObject.SetActive(true);

        if (projectile != null)
        {
            Vector3 _randomSpreadDirection;
            if (totalProjectiles > 1)
            {
                _randomSpreadDirection.x = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.x, Spread.x);
                _randomSpreadDirection.y = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.y, Spread.y);
                _randomSpreadDirection.z = MMMaths.Remap(projectileIndex, 0, totalProjectiles - 1, -Spread.z, Spread.z);
            }
            else
            {
                _randomSpreadDirection = Vector3.zero;
            }
            Quaternion spread = Quaternion.Euler(_randomSpreadDirection);

            // Find the nearest enemy
            GameObject nearestEnemy = ProgrammableWeaponCommonFunctions.FindNearestCharacter(owner.transform.position, tag:"Player");
            Vector3 direction = Vector3.right;
            if (nearestEnemy != null)
            {
                // Calculate direction towards the nearest enemy
                direction = (nearestEnemy.transform.position - owner.transform.position).normalized;
            }

            // 计算 Z 轴旋转角度（2D 平面中绕 Z 轴旋转）
            float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;

            // Set the projectile's direction
            projectile.SetDirection(spread * direction, Quaternion.Euler(0, 0, angle), true);
        }

        if (triggerObjectActivation)
        {
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }

        return (nextGameObject);
    }
}