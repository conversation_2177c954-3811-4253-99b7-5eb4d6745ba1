using UnityEngine;
using cfg.bonus;
using cfg.drop;
using cfg.item;
using cfg.role;

namespace Test
{
    /// <summary>
    /// Test script to verify data table access fixes
    /// </summary>
    public class DataTableTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public bool testOnStart = false;
        public int testBonusId = 10602;
        public int testMonsterId = 10001;
        public int testItemId = 10001;
        public int testDropId = 10001;

        void Start()
        {
            if (testOnStart)
            {
                TestDataTables();
            }
        }

        [ContextMenu("Test Data Tables")]
        public void TestDataTables()
        {
            Debug.Log("=== Data Table Test Started ===");

            // Test DataTableManager initialization
            if (DataTableManager.Instance == null)
            {
                Debug.LogError("DataTableManager.Instance is null");
                return;
            }
            Debug.Log("✓ DataTableManager.Instance exists");

            // Test TbBonus
            TestBonusTable();

            // Test TbMonster
            TestMonsterTable();

            // Test TbItems
            TestItemsTable();

            // Test TbDrop
            TestDropTable();

            Debug.Log("=== Data Table Test Completed ===");
        }

        private void TestBonusTable()
        {
            Debug.Log("--- Testing TbBonus ---");
            
            // Test with valid ID
            var bonus1 = DataTableManager.Instance.Tables.TbBonus.GetOrDefault(10001);
            if (bonus1 != null)
            {
                Debug.Log($"✓ Found bonus 10001: Mode = {bonus1.Mode}");
            }
            else
            {
                Debug.LogWarning("⚠ Bonus 10001 not found");
            }

            // Test with invalid ID (the one causing the error)
            var bonus2 = DataTableManager.Instance.Tables.TbBonus.GetOrDefault(testBonusId);
            if (bonus2 != null)
            {
                Debug.Log($"✓ Found bonus {testBonusId}: Mode = {bonus2.Mode}");
            }
            else
            {
                Debug.LogWarning($"⚠ Bonus {testBonusId} not found (this is expected if it doesn't exist)");
            }

            // Test the old Get method (should throw exception for invalid ID)
            try
            {
                var bonus3 = DataTableManager.Instance.Tables.TbBonus.Get(testBonusId);
                Debug.Log($"✓ Get method worked for bonus {testBonusId}");
            }
            catch (System.Collections.Generic.KeyNotFoundException)
            {
                Debug.LogWarning($"⚠ Get method threw KeyNotFoundException for bonus {testBonusId} (this is expected)");
            }
        }

        private void TestMonsterTable()
        {
            Debug.Log("--- Testing TbMonster ---");
            
            var monster = DataTableManager.Instance.Tables.TbMonster.GetOrDefault(testMonsterId);
            if (monster != null)
            {
                Debug.Log($"✓ Found monster {testMonsterId}: Name = {monster.Name}, ResourceName = {monster.ResourceName}");
            }
            else
            {
                Debug.LogWarning($"⚠ Monster {testMonsterId} not found");
            }
        }

        private void TestItemsTable()
        {
            Debug.Log("--- Testing TbItems ---");
            
            var item = DataTableManager.Instance.Tables.TbItems.GetOrDefault(testItemId);
            if (item != null)
            {
                Debug.Log($"✓ Found item {testItemId}: Name = {item.Name}, Type = {item.Type}, PrefabPath = {item.PrefabPath}");
            }
            else
            {
                Debug.LogWarning($"⚠ Item {testItemId} not found");
            }
        }

        private void TestDropTable()
        {
            Debug.Log("--- Testing TbDrop ---");
            
            var drop = DataTableManager.Instance.Tables.TbDrop.GetOrDefault(testDropId);
            if (drop != null)
            {
                Debug.Log($"✓ Found drop {testDropId}: Id = {drop.Id}");
            }
            else
            {
                Debug.LogWarning($"⚠ Drop {testDropId} not found");
            }
        }

        [ContextMenu("List Available Bonus IDs")]
        public void ListAvailableBonusIds()
        {
            Debug.Log("=== Available Bonus IDs ===");
            
            if (DataTableManager.Instance?.Tables?.TbBonus?.DataList != null)
            {
                foreach (var bonus in DataTableManager.Instance.Tables.TbBonus.DataList)
                {
                    Debug.Log($"Bonus ID: {bonus.Id}, Mode: {bonus.Mode}");
                }
            }
            else
            {
                Debug.LogError("TbBonus.DataList is null");
            }
        }

        [ContextMenu("Test SIXGameManager Safety")]
        public void TestSIXGameManagerSafety()
        {
            Debug.Log("=== Testing SIXGameManager Safety ===");

            // Test if SIXGameManager exists
            if (SIXGameManager.Instance == null)
            {
                Debug.LogWarning("SIXGameManager.Instance is null - this is expected if not in game scene");
                return;
            }

            Debug.Log("✓ SIXGameManager.Instance exists");

            // Test object pool
            if (SIXGameManager.Instance.skillPool != null)
            {
                Debug.Log("✓ SIXGameManager.Instance.skillPool exists");
                
                if (SIXGameManager.Instance.skillPool is MoreMountains.Tools.MMMultipleObjectPooler pooler)
                {
                    Debug.Log("Pool statistics: " + pooler.GetPoolStatistics());
                }
            }
            else
            {
                Debug.LogWarning("⚠ SIXGameManager.Instance.skillPool is null");
            }
        }
    }
}
