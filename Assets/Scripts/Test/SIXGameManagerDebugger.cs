using UnityEngine;
using System.Collections.Generic;

namespace Test
{
    /// <summary>
    /// Debug script to help identify SIXGameManager issues
    /// </summary>
    public class SIXGameManagerDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        public bool enableDebugLogs = true;
        public bool logOnCharacterDeath = true;

        void Start()
        {
            if (enableDebugLogs)
            {
                Debug.Log("=== SIXGameManagerDebugger Started ===");
                LogCurrentGameState();
            }
        }

        [ContextMenu("Log Current Game State")]
        public void LogCurrentGameState()
        {
            Debug.Log("=== Current Game State ===");

            // Check PlayerData
            if (PlayerData.GetInstance() == null)
            {
                Debug.LogError("PlayerData.GetInstance() is null");
                return;
            }

            var levelData = PlayerData.GetInstance().LevelData;
            if (levelData == null)
            {
                Debug.LogError("PlayerData.GetInstance().LevelData is null");
                return;
            }

            Debug.Log($"Current MapID: {levelData.MapID}");
            Debug.Log($"Current MonsterRefreshIndex: {levelData.CurMonsterRefreshIndex}");
            Debug.Log($"Current Section: {PlayerData.GetInstance().CurSection}");
            Debug.Log($"Current Level: {PlayerData.GetInstance().CurLevel}");

            // Check DataTableManager
            if (DataTableManager.Instance == null)
            {
                Debug.LogError("DataTableManager.Instance is null");
                return;
            }

            // Check TbMap
            var mapCfg = DataTableManager.Instance.Tables.TbMap.GetOrDefault(levelData.MapID);
            if (mapCfg == null)
            {
                Debug.LogError($"Map configuration with ID {levelData.MapID} not found");
                return;
            }

            Debug.Log($"Map MonsterRefreshesData count: {mapCfg.MonsterRefreshesData.Count}");

            // Check if current refresh index is valid
            int refreshIndex = levelData.CurMonsterRefreshIndex - 1;
            if (refreshIndex < 0 || refreshIndex >= mapCfg.MonsterRefreshesData.Count)
            {
                Debug.LogError($"Invalid refresh index: {refreshIndex}, valid range: 0 to {mapCfg.MonsterRefreshesData.Count - 1}");
            }
            else
            {
                Debug.Log($"✓ Refresh index {refreshIndex} is valid");
                var monsters = mapCfg.MonsterRefreshesData[refreshIndex];
                Debug.Log($"Current wave has {monsters.Count} monsters");
            }

            // Check SIXGameManager
            if (SIXGameManager.Instance == null)
            {
                Debug.LogWarning("SIXGameManager.Instance is null");
                return;
            }

            Debug.Log("✓ SIXGameManager.Instance exists");

            // Check SIXLevelManager
            if (SIXLevelManager.Current == null)
            {
                Debug.LogWarning("SIXLevelManager.Current is null");
            }
            else
            {
                Debug.Log($"SIXLevelManager doors count: {SIXLevelManager.Current.PointsOfDoors?.Length ?? 0}");
            }
        }

        [ContextMenu("Test Monster Refresh Safety")]
        public void TestMonsterRefreshSafety()
        {
            Debug.Log("=== Testing Monster Refresh Safety ===");

            if (PlayerData.GetInstance()?.LevelData == null)
            {
                Debug.LogError("PlayerData or LevelData is null");
                return;
            }

            var levelData = PlayerData.GetInstance().LevelData;
            var mapCfg = DataTableManager.Instance.Tables.TbMap.GetOrDefault(levelData.MapID);
            
            if (mapCfg == null)
            {
                Debug.LogError($"Map configuration not found for ID: {levelData.MapID}");
                return;
            }

            Debug.Log($"Testing refresh indices for map {levelData.MapID}:");
            Debug.Log($"MonsterRefreshesData count: {mapCfg.MonsterRefreshesData.Count}");
            Debug.Log($"Current CurMonsterRefreshIndex: {levelData.CurMonsterRefreshIndex}");

            // Test all possible refresh indices
            for (int i = 0; i < mapCfg.MonsterRefreshesData.Count; i++)
            {
                try
                {
                    var monsters = mapCfg.MonsterRefreshesData[i];
                    Debug.Log($"✓ Index {i}: {monsters.Count} monsters");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"✗ Index {i}: {e.Message}");
                }
            }

            // Test current index
            int currentIndex = levelData.CurMonsterRefreshIndex - 1;
            if (currentIndex >= 0 && currentIndex < mapCfg.MonsterRefreshesData.Count)
            {
                Debug.Log($"✓ Current index {currentIndex} is safe");
            }
            else
            {
                Debug.LogError($"✗ Current index {currentIndex} is out of range!");
            }
        }

        [ContextMenu("Test Section Configuration Safety")]
        public void TestSectionConfigurationSafety()
        {
            Debug.Log("=== Testing Section Configuration Safety ===");

            if (PlayerData.GetInstance() == null)
            {
                Debug.LogError("PlayerData.GetInstance() is null");
                return;
            }

            int curSection = PlayerData.GetInstance().CurSection;
            int curLevel = PlayerData.GetInstance().CurLevel;

            Debug.Log($"Testing current section: {curSection}, level: {curLevel}");

            var curScf = DataTableManager.Instance.Tables.TbSection.GetOrDefault(section: curSection, level: curLevel);
            if (curScf == null)
            {
                Debug.LogError($"Current section configuration not found: Section {curSection}, Level {curLevel}");
            }
            else
            {
                Debug.Log($"✓ Current section configuration found");
                Debug.Log($"RandomMapLibrary count: {curScf.RandomMapLibrary?.Count ?? 0}");
                Debug.Log($"MapDoorHangingPoint count: {curScf.MapDoorHangingPoint?.Count ?? 0}");
                Debug.Log($"RandomBonusMode count: {curScf.RandomBonusMode?.Count ?? 0}");
            }

            // Test next section
            var (nextSection, nextLevel) = Formula.GetNextSection();
            Debug.Log($"Testing next section: {nextSection}, level: {nextLevel}");

            if (nextSection != 0 || nextLevel != 0)
            {
                var nextScf = DataTableManager.Instance.Tables.TbSection.GetOrDefault(section: nextSection, level: nextLevel);
                if (nextScf == null)
                {
                    Debug.LogError($"Next section configuration not found: Section {nextSection}, Level {nextLevel}");
                }
                else
                {
                    Debug.Log($"✓ Next section configuration found");
                }
            }
            else
            {
                Debug.Log("Game completed - no next section");
            }
        }

        [ContextMenu("Simulate Character Death Event")]
        public void SimulateCharacterDeathEvent()
        {
            Debug.Log("=== Simulating Character Death Event ===");
            
            if (SIXGameManager.Instance == null)
            {
                Debug.LogError("SIXGameManager.Instance is null - cannot simulate");
                return;
            }

            // Log current state before simulation
            LogCurrentGameState();

            Debug.Log("This would trigger OnMMEvent in SIXGameManager...");
            Debug.Log("Check the logs above to see if there would be any index out of range issues");
        }
    }
}
