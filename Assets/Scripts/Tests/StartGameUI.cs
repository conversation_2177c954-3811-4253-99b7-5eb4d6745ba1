using MoreMountains.TopDownEngine;
using System.Collections;
using System.Collections.Generic;
using Battle.Utils;
using UnityEngine;
using UnityEngine.Rendering;

public class StartGameUI : MonoBehaviour
{
    private string _inputSpellId = "10001";
    private void Awake()
    {
        GameUIManager.GetInstance().OpenUI(typeof(MainLeftTopScene));
        GameUIManager.GetInstance().OpenUI(typeof(MainLeftBottomScene));
        GameUIManager.GetInstance().OpenUI(typeof(MainJoystickScreen));
    }
    // Start is called before the first frame update
    void Start()
    {
    }



    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Debugger");
        if (GUILayout.Button("添加一个随机法术"))
        {
            var count = DataTableManager.Instance.Tables.TbSpell.DataList.Count;
            int index = Random.Range(0, count);
            BattleHelper.AddSlotElement(DataTableManager.Instance.Tables.TbSpell.DataList[index].Id);
        }

        _inputSpellId = GUILayout.TextField(_inputSpellId, 25);
        if (GUILayout.Button("添加法术"))
        {
            if (int.TryParse(_inputSpellId, out int skillId))
            {
                BattleHelper.AddSlotElement(skillId);
            }
        }
        
        if(GUILayout.Button("随机一把武器"))
        {
            int index = Random.Range(1001, 1003);
            Debug.Log(string.Format("随机武器: {0}", index));
            PlayerData.GetInstance().MagicStaffID = (uint)index;
        }
        
        GUILayout.EndArea();
    }

}
