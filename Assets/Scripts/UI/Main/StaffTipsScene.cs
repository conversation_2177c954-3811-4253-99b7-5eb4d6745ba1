using MoreMountains.Tools;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class StaffTipsSceneParam : UIOpenScreenParameterBase
{
    public MagicStaff magicStaff; // 法杖
    public StaffTipsSceneParam(MagicStaff magicStaff)
    {
        this.magicStaff = magicStaff;
    }
}


public class StaffTipsScene : ScreenBase, MMEventListener<MagicStaffIDChangedEvent>
{
    StaffTipsCtrl mCtrl;
    MagicStaff _magicStaff;
    StaffTipsSceneParam mParam;

    public StaffTipsScene(UIOpenScreenParameterBase param = null) : base(UIConst.UIStaffTips, param)
    {
        
    }

    public override void OnClose()
    {
        base.OnClose();
        this.MMEventStopListening<MagicStaffIDChangedEvent>();
    }

    protected override void OnLoadSuccess()
    {
        base.OnLoadSuccess();
        mCtrl = mCtrlBase as StaffTipsCtrl;

        mCtrl.GetComponent<Button>().onClick.AddListener(() =>
        {
            GameUIManager.GetInstance().CloseUI(this.GetType());
        });
        this.MMEventStartListening<MagicStaffIDChangedEvent>();

        mCtrl.BaseProperties.ForEach(go => go.SetActive(false));
        mCtrl.ExtraProperties.ForEach(go => go.SetActive(false));

        mParam = mOpenParam as StaffTipsSceneParam;
        _magicStaff = mParam.magicStaff;
        UpdateTips();
        UpdateBuffs();
    }

    public void UpdateTips()
    {
        mCtrl.Title.text = _magicStaff.Name;
        // mCtrl.ItemType.text = _magicStaff.Type.ToString(); // 类型

        // 更新基础属性
        var baseProperties = _magicStaff.GetBasePropertiesWithTypes();

        int index = 0;
        foreach (var item_prop in baseProperties)
        { 
            if (index >= mCtrl.BaseProperties.Count)
            {
                GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.BaseProperties[0].gameObject, mCtrl.BaseProperties[0].transform.parent);
                mCtrl.BaseProperties.Add(newObject);
            }

            mCtrl.BaseProperties[index].SetActive(true);
            object value = null;
            if (item_prop.Value.Type == typeof(float))
            {
                value = Convert.ToSingle(item_prop.Value.Value);
            }
            if (value != null)
            {
                mCtrl.BaseProperties[index].GetComponent<TextMeshProUGUI>().text = string.Format("{0}:{1}", _magicStaff.BaseProperties[item_prop.Key], value);
            }
            

            index++;
        }

        for (int i = baseProperties.Count; i < mCtrl.BaseProperties.Count; i++)
        {
            mCtrl.BaseProperties[i].SetActive(false);
        }

        // 更新高级属性
        var extraProperties = _magicStaff.GetExtendedPropertiesWithTypes();

        index = 0;
        foreach (var item_prop in extraProperties)
        { 
            if (index >= mCtrl.ExtraProperties.Count)
            {
                GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.ExtraProperties[0].gameObject, mCtrl.ExtraProperties[0].transform.parent);
                mCtrl.ExtraProperties.Add(newObject);
            }

            mCtrl.ExtraProperties[index].SetActive(true);
            object value = null;
            if (item_prop.Value.Type == typeof(float))
            {
                value = Convert.ToSingle(item_prop.Value.Value);
            }
            if (value!= null)
            {
                mCtrl.ExtraProperties[index].GetComponent<TextMeshProUGUI>().text = string.Format("{0}:{1}", _magicStaff.ExtraProperties[item_prop.Key], value);
            }
            
            index++;
        }

        for (int i = extraProperties.Count; i < mCtrl.ExtraProperties.Count; i++)
        {
            mCtrl.ExtraProperties[i].SetActive(false);
        }

        // 强制调整Tips位置
        mCtrl.forceAdjustPnlPos = true;

    }

    public void UpdateBuffs()
    {
        // step 1. 隐藏所有目标槽位
        for (int i = 0; i < mCtrl.EquipedBuffs.Count; i++ )
        {
            mCtrl.EquipedBuffs[i].GetComponentInChildren<MagicSkillItemSubCtrl>().gameObject.SetActive(false);
        }

        for (int i = mCtrl.EquipedBuffs.Count; i < _magicStaff.SlotNumber; i++)
        {
            GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.EquipedBuffs[0].gameObject, mCtrl.EquipedBuffs[0].transform.parent);
            // newObject.GetComponentInChildren<MagicSkillItemSubCtrl>().gameObject.SetActive(false);
            foreach (Transform child in newObject.transform)
            { // 隐藏所有子节点
                child.gameObject.SetActive(false);
            }
            mCtrl.EquipedBuffs.Add(newObject);
        }

        for (int i = (int)_magicStaff.SlotNumber; i < mCtrl.EquipedBuffs.Count; i++)
        {
            mCtrl.EquipedBuffs[i].SetActive(false);
        }

        // step 2. 获取当前玩家已装备的Buff
        List<SlotElement> slots = new List<SlotElement>();
        PlayerData.GetInstance().OwnedInMagicPack.ForEach(item =>
        {
            if (item.SlotType == 1) // 在法杖中的
            {
                slots.Add(item);
            }
        });

        slots.Sort((a, b) => a.SlotIndex.CompareTo(b.SlotIndex));

        // step 3. 显示装备
        foreach(var slot in slots) 
        {
            mCtrl.EquipedBuffs[slot.SlotIndex].SetActive(true);
            foreach (Transform child in mCtrl.EquipedBuffs[slot.SlotIndex].transform)
            { // 显示所有子节点
                child.gameObject.SetActive(true);
            }
            // mCtrl.EquipedBuffs[i].GetComponentInChildren<MagicSkillItemSubCtrl>().gameObject.SetActive(true);
            var item = DataTableManager.Instance.Tables.TbItems.Get(slot.ID);
            Sprite newIconSprite = Resources.Load<Sprite>($"Items/Skill/{item?.Icon}");
            if (newIconSprite != null)
            {
                mCtrl.EquipedBuffs[slot.SlotIndex].GetComponentInChildren<MagicSkillItemSubCtrl>().Icon.sprite = newIconSprite;
            }
            else
            {
                Debug.LogWarning("Buff Icon资源未找到！");
            }
           
        }
    }

    protected override void UIAdapt(Vector2Int res)
    {
        // Debug.Log(string.Format("分辨率发生了变化，宽为{0},高为{1}", res.x, res.y));
    }

    public void OnMMEvent(MagicStaffIDChangedEvent eventType)
    {
        if (eventType.EventName == "StaffIDChanged")
        {
            // 根据数据表， 获取魔法杖数据
            // key:eventType.UintParameter
            cfg.item.Weapon tbWeapon = DataTableManager.Instance.Tables.TbWeapon.Get((int)eventType.UintParameter);
            _magicStaff = new MagicStaff(tbWeapon);
            UpdateTips();
            UpdateBuffs();
        }
    }

}
