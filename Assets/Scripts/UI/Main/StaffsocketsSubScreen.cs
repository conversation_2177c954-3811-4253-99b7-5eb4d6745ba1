using MoreMountains.Tools;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class StaffsocketsSubScreen : SubScreenBase, MMEventListener<OwnedMagicChangedEvent>
{
    StaffsocketsSubCtrl mCtrl;
    private float m_HoleDeltaDist = 0f;
    private List<GameObject> m_ListSlots = new List<GameObject>();// 插槽
    private List<GameObject> m_ListSkills = new List<GameObject>(); // 法术对象

    public List<GameObject> CurSlots
    {
        get => m_ListSlots;
    }

    public StaffsocketsSubScreen(StaffsocketsSubCtrl subCtrl) : base(subCtrl)
    {
    }

    protected override void Init()
    {
        base.Init();
        mCtrl = mCtrlBase as StaffsocketsSubCtrl;

        m_HoleDeltaDist = mCtrl.Hole001.rectTransform.anchoredPosition.x - mCtrl.Hole000.rectTransform.anchoredPosition.x;

        mCtrl.Hole000.gameObject.SetActive(false);
        mCtrl.Hole001.gameObject.SetActive(false);
        mCtrl.Hole002.gameObject.SetActive(false);

        m_ListSlots.Add(mCtrl.Hole000.gameObject);
        m_ListSlots.Add(mCtrl.Hole001.gameObject);
        m_ListSlots.Add(mCtrl.Hole002.gameObject);

        mCtrl.MagicSkillTransform.gameObject.SetActive(false);
        m_ListSkills.Add(mCtrl.MagicSkillTransform.gameObject);

        this.MMEventStartListening<OwnedMagicChangedEvent>();
    }

    public override void Dispose()
    {
        base.Dispose();
        this.MMEventStopListening<OwnedMagicChangedEvent>();
    }

    public void UpdateMagicSkills(List<SlotElement> slotElements)
    {
        // 确保技能GameObject 可以匹配上插槽数目
        for (int i = 0; i < m_ListSlots.Count; i++)
        {
            if (i >= m_ListSkills.Count)
            { // 增加技能GameObject
                GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.MagicSkillTransform.gameObject, mCtrl.transform);
                m_ListSkills.Add(newObject);
            }

            // 设置技能位置
            m_ListSkills[i].GetComponent<RectTransform>().anchoredPosition = m_ListSlots[i].GetComponent<RectTransform>().anchoredPosition;
            m_ListSkills[i].SetActive(false);
        }

        for (int i = 0; i < slotElements.Count; i++)
        {
            SlotElement se = slotElements[i];
            if (se.SlotType != 1) // 不在法杖中的元素不考虑
                continue;

            m_ListSkills[se.SlotIndex].SetActive(true);
            MagicSkillItemSubCtrl magicSkillItemSubCtrl = m_ListSkills[se.SlotIndex].GetComponent<MagicSkillItemSubCtrl>();
            magicSkillItemSubCtrl.SlotElement = se;

            var item = DataTableManager.Instance.Tables.TbItems.Get(se.ID);
            Sprite newIconSprite = Resources.Load<Sprite>($"Items/Skill/{item?.Icon}");
            if (newIconSprite != null)
            {
                magicSkillItemSubCtrl.Icon.sprite = newIconSprite;
            }
        }        


        for (int i = 0; i < m_ListSkills.Count; i++)
        {
            if (!m_ListSkills[i].activeSelf)
            {
                MagicSkillItemSubCtrl magicSkillItemSubCtrl = m_ListSkills[i].GetComponent<MagicSkillItemSubCtrl>();
                magicSkillItemSubCtrl.SlotElement = null;
            }
        }
    }

    public void UpdateStaffSockets(MagicStaff magicStaff)
    {
        if (m_ListSlots == null)
        {
            Debug.LogError("m_ListSlots is null");
        }

        if (magicStaff == null)
        {
            Debug.LogError("magicStaff is null");
        }

        if (m_ListSlots.Count >= magicStaff.SlotNumber)
        {
            for (int i = 0; i < magicStaff.SlotNumber; i++)
            {
                m_ListSlots[i].SetActive(true);
            }

            for (int i = (int)magicStaff.SlotNumber; i < m_ListSlots.Count; i++)
            {
                m_ListSlots[i].SetActive(false);
            }

        }
        else if (m_ListSlots.Count < magicStaff.SlotNumber)
        {
            for (int i = 0; i < m_ListSlots.Count; i++)
            {
                m_ListSlots[i].SetActive(true);
            }

            for (int i = m_ListSlots.Count; i < magicStaff.SlotNumber; i++)
            {
                GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.Hole000.gameObject, mCtrl.transform);
                RectTransform rectTransform = newObject.GetComponent<RectTransform>();
                rectTransform.anchoredPosition = new Vector2(i * m_HoleDeltaDist, 0);
                newObject.name = "SpawnedObject_" + i;
                newObject.SetActive(true);
                m_ListSlots.Add(newObject);
            }
        }

    }

    public void OnMMEvent(OwnedMagicChangedEvent eventType)
    {
        if (eventType.EventName == "MagicPackChanged")
        {
            List<SlotElement> allElements = eventType.ListParameter as List<SlotElement>;
            UpdateMagicSkills(allElements);

        }
    }
}