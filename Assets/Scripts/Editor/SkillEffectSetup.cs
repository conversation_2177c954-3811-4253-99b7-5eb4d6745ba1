#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;

namespace Editor
{
    /// <summary>
    /// Editor script to help setup skill effects in the Resources folder
    /// </summary>
    public class SkillEffectSetup : EditorWindow
    {
        [MenuItem("Tools/Setup Skill Effects")]
        public static void ShowWindow()
        {
            GetWindow<SkillEffectSetup>("Skill Effect Setup");
        }

        private void OnGUI()
        {
            GUILayout.Label("Skill Effect Setup", EditorStyles.boldLabel);
            GUILayout.Space(10);

            GUILayout.Label("This tool will copy skill effect prefabs to the Resources folder", EditorStyles.helpBox);
            GUILayout.Space(10);

            if (GUILayout.Button("Copy Spell10002Hit to Resources"))
            {
                CopySpell10002HitToResources();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Copy All Skill Effects to Resources"))
            {
                CopyAllSkillEffectsToResources();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("List Missing Skill Effects"))
            {
                ListMissingSkillEffects();
            }
        }

        private static void CopySpell10002HitToResources()
        {
            string sourcePath = "Assets/Prefabs/Skill/Spell10002Hit.prefab";
            string targetDir = "Assets/Resources/Effects";
            string targetPath = "Assets/Resources/Effects/Spell10002Hit.prefab";

            // Create target directory if it doesn't exist
            if (!Directory.Exists(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }

            // Check if source exists
            if (!File.Exists(sourcePath))
            {
                Debug.LogError($"Source file not found: {sourcePath}");
                return;
            }

            // Copy the file
            try
            {
                AssetDatabase.CopyAsset(sourcePath, targetPath);
                AssetDatabase.Refresh();
                Debug.Log($"Successfully copied {sourcePath} to {targetPath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to copy file: {e.Message}");
            }
        }

        private static void CopyAllSkillEffectsToResources()
        {
            string sourceDir = "Assets/Prefabs/Skill";
            string targetDir = "Assets/Resources/Effects";

            // Create target directory if it doesn't exist
            if (!Directory.Exists(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }

            // Find all prefabs in the source directory
            string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab", new[] { sourceDir });

            int copiedCount = 0;
            foreach (string guid in prefabGuids)
            {
                string sourcePath = AssetDatabase.GUIDToAssetPath(guid);
                string fileName = Path.GetFileName(sourcePath);
                string targetPath = Path.Combine(targetDir, fileName);

                try
                {
                    AssetDatabase.CopyAsset(sourcePath, targetPath);
                    copiedCount++;
                    Debug.Log($"Copied: {fileName}");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to copy {fileName}: {e.Message}");
                }
            }

            AssetDatabase.Refresh();
            Debug.Log($"Successfully copied {copiedCount} skill effect prefabs to Resources/Effects");
        }

        private static void ListMissingSkillEffects()
        {
            string[] requiredEffects = {
                "Spell10001Hit",
                "Spell10002Hit",
                "Spell10003Hit",
                "Spell10004Hit",
                "Spell10005Hit",
                "Spell10006Hit",
                "Spell10007Hit",
                "Spell10008Hit"
            };

            Debug.Log("=== Checking for missing skill effects ===");

            foreach (string effectName in requiredEffects)
            {
                // Check in Resources/Effects
                GameObject resourcesEffect = Resources.Load<GameObject>($"Effects/{effectName}");
                
                // Check in Prefabs/Skill
                GameObject prefabEffect = AssetDatabase.LoadAssetAtPath<GameObject>($"Assets/Prefabs/Skill/{effectName}.prefab");

                if (resourcesEffect != null)
                {
                    Debug.Log($"✓ {effectName} found in Resources/Effects");
                }
                else if (prefabEffect != null)
                {
                    Debug.LogWarning($"⚠ {effectName} found in Prefabs/Skill but not in Resources/Effects");
                }
                else
                {
                    Debug.LogError($"✗ {effectName} not found anywhere");
                }
            }

            Debug.Log("=== Check complete ===");
        }
    }
}
#endif
