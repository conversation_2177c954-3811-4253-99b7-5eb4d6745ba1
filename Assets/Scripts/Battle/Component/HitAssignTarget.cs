using System;
using Battle.MMCore;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace Battle.Component
{
    [RequireComponent(typeof(TargetProjectTileExt))]
    [RequireComponent(typeof(SixDamageOnTouch))]
    public class HitAssignTarget : MonoBehaviour
    {
        public float range = 10;
        private SixDamageOnTouch _sixDamageOnTouch;

        public void Awake()
        {
            _sixDamageOnTouch = GetComponent<SixDamageOnTouch>();
        }

        public void OnEnable()
        {
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.AddListener(OnHit);
            }
           

        }

        private void OnHit(Health target)
        {
            var results = ProgrammableWeaponCommonFunctions.FindRangeEntity(transform.position, gameObject.name, range);
            foreach (var result in results)
            {
                var projectTileExt = result.GetComponent<TargetProjectTileExt>();
                if (projectTileExt)
                {
                    projectTileExt.TrySetTarget(target.gameObject);
                }
            }
        }

        public void OnDisable()
        {
            if (_sixDamageOnTouch)
            {
                _sixDamageOnTouch.HitDamageableEvent.RemoveListener(OnHit);
            }
        }
    }
}