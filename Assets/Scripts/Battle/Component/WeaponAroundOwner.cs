using System;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.Serialization;

namespace Battle.Component
{
    public class WeaponAroundOwner : MonoBehaviour
    {
        private Transform _ownerTransform;

        [SerializeField] private float aroundRadius = 2;
        [SerializeField] private float aroundAngularVelocity = 60;
        [SerializeField] private Vector3 targetOffset = new float3(0, 0, 0);
        [SerializeField] private Transform fireTransform;
        [SerializeField] private Transform rotationAimTransform;
        public Transform FireTransform => fireTransform;

        private Vector3 _ideaTargetPosition;

        private bool _haveAimTarget = false;

        private Transform _aimTargetTransform;
        private Vector3 _lastDirection = Vector3.up;
       // private readonly  _initDirection = Vector3.up;

        private void Update()
        {
            if (_ownerTransform == null)
            {
                return;
            }

            if (_haveAimTarget)
            {
                _ideaTargetPosition = targetOffset + _ownerTransform.position + Vector3.Normalize(_aimTargetTransform.position - _ownerTransform.transform.position) * aroundRadius;
                rotationAimTransform.up = (_aimTargetTransform.position - _ideaTargetPosition).normalized;
            }
            else
            {
                _lastDirection = Quaternion.AngleAxis(aroundAngularVelocity * Time.deltaTime, Vector3.forward) *
                                 _lastDirection;
                _ideaTargetPosition = targetOffset + _ownerTransform.position +
                                      _lastDirection * aroundRadius;
                rotationAimTransform.rotation = Quaternion.identity;
                
            }
            gameObject.transform.position = _ideaTargetPosition;
        }

        public void SetOwner(GameObject ownerGameObject)
        {
            _ownerTransform = ownerGameObject.transform;
        }

        public void ClearTarget()
        {
            _haveAimTarget = false;
        }
        
        public void SetAimTargetPosition(Transform target)
        {
            _haveAimTarget = true;
            _aimTargetTransform = target;
        }

        public bool IsAimReady()
        {
            //todo 插值
            return true;
        }
        
    }
}