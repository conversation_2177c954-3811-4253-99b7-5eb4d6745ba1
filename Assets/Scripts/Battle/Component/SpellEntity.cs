using System;
using System.Collections.Generic;
using cfg.skill;
using MoreMountains.Tools;
using UnityEngine;
using UnityEngine.UIElements;

namespace Battle.Skill
{
    [RequireComponent(typeof(MMPoolableObject))]
    public class SpellEntity : MonoBehaviour
    {
        public float prefabRange = 1;
        public bool rangeX = true;
        public bool rangeY = true;
        private MMPoolableObject _object;
        
        public delegate void BeforeDeathExecute(GameObject go);
        public event BeforeDeathExecute BeforeDeathEvent;
        public SpellBaseData SpellBaseData
        {
            private set; 
            get;
        }

        public List<BuffElement> ActiveBuffs
        {
            private set;
            get;
        }
        public void Awake()
        {
            _object = GetComponent<MMPoolableObject>();
        }

        public void OnEnable()
        {
            _object.BeforeDeath += ApplyBuffBeforeDeath;
        }


        public void Set(SpellBaseData spellBaseData, List<BuffElement> activeBuffs)
        {
            ActiveBuffs = activeBuffs;
            SpellBaseData = spellBaseData;
            var range = spellBaseData.GetValue(SpellAttribute.Range);
            var xScale = rangeX ? range / prefabRange : 1;
            var yScale = rangeY ? range / prefabRange : 1;
            transform.localScale = new Vector3(xScale, yScale, 1);
        }

        private void ApplyBuffBeforeDeath(GameObject go)
        {
            if (ActiveBuffs != null)
            {
                foreach (var buffElement in ActiveBuffs)
                {
                    SpellBaseData.ApplyBuff(buffElement.BuffLevelCfg.Effects, BuffEffectivePoint.BeforeDeath);
                }
            }

            BeforeDeathEvent?.Invoke(go);
        }

        public void OnDisable()
        {
            _object.BeforeDeath -= ApplyBuffBeforeDeath;
        }

    }
}