using System;
using MoreMountains.Tools;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Pool;
using UnityEngine.Serialization;

namespace Battle.Component
{
    //[ExecuteInEditMode]
    public class LinkComponent : MonoBehaviour
    {
        [Tooltip("右或者上，其他无效")]
        public MoveDirection directionMode = MoveDirection.Right;
        public Transform effectStart;
        public Transform effectEnd;
        private float _myLength; 
        private Transform _linkTarget;
        private Transform _linkStart;

        private MMPoolableObject _pooledObject;
        public void Awake()
        {
            _myLength = Vector3.Distance(effectEnd.position, effectStart.position);
            _pooledObject = GetComponent<MMPoolableObject>();
        }

        public void OnEnable()
        {
            transform.localScale = Vector3.one;
        }
        
        

        public void Update()
        {
            if (_linkStart && _linkTarget)
            {
                SetLinkPosition(_linkStart.position, _linkTarget.position);
            }
        }

        public void SetLink(Transform linkStart, Transform linkTarget)
        {
            _linkStart = linkStart;
            _linkTarget = linkTarget;
        }

        public void SetLinkPosition(Vector3 linkStart, Vector3 linkTarget)
        {
            gameObject.transform.position = linkStart;
            var linkDistance = Vector3.Distance(linkStart, linkTarget);
            var scale = linkDistance / _myLength;
                
            var newDirection = (linkTarget - linkStart).normalized;
            switch (directionMode)
            {
                case MoveDirection.Right:
                    transform.right = newDirection;
                    effectStart.transform.localScale = new Vector3(scale, 1, 1);
                    break;
                case MoveDirection.Up:
                    transform.up = newDirection;
                    effectStart.transform.localScale = new Vector3(1, scale, 1);
                    break;
            }
        }

        public void OnDisable()
        {
            transform.localScale = Vector3.one;
        }
    }
}