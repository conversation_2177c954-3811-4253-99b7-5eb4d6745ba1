using System;
using System.Collections.Generic;
using Battle.Skill;
using Battle.Utils;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.Pool;
using Random = UnityEngine.Random;

namespace Battle.Component
{
    public class EndSplitComponent : MonoBehaviour
    {
        #region Constants
        private const int c_ExcludedBuffID = 20003; // 需要排除的Buff ID（防止无限分裂）
        #endregion

        #region Private Fields
        private bool m_UniformScatter = true;
        private SpellEntity m_SpellEntity;
        private Projectile m_Projectile;
        #endregion

        #region Public Fields
        public float scatter = 270;
        public int splitCount = 3;
        #endregion

        #region Unity Lifecycle
        public void Awake()
        {
            m_SpellEntity = GetComponent<SpellEntity>();
            m_Projectile = GetComponent<Projectile>();
        }

        public void OnEnable()
        {
            m_SpellEntity.BeforeDeathEvent += Execute;
        }

        public void OnDisable()
        {
            m_SpellEntity.BeforeDeathEvent -= Execute;
        }
        #endregion

        #region Private Methods
        private void Execute(GameObject hitGo)
        {
            var offset = 1f;
            var hitCollider = hitGo?.GetComponent<Collider2D>();
            var health = hitGo?.GetComponent<Health>();
            var spawnPosition = transform.position;
            if (hitGo)
            {
                spawnPosition = hitGo.transform.position;
            }

            if (hitCollider && health)
            {
                offset = Math.Max(hitCollider.bounds.extents.x, hitCollider.bounds.extents.y);
                offset = Math.Max(offset, hitCollider.bounds.extents.z);
                offset *= 2;
            }

            if (m_SpellEntity.rangeX && m_SpellEntity.rangeY)
            {
                offset += m_SpellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
            }
            else if (!hitGo && (m_SpellEntity.rangeX || m_SpellEntity.rangeY))
            {
                spawnPosition += m_Projectile.Direction * m_SpellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
            }

            // 使用字典优化Buff查找和过滤
            var filteredBuffs = GetFilteredBuffs(m_SpellEntity.ActiveBuffs);

            //随机一个方向
            var direction = Random.insideUnitCircle.normalized;
            BattleHelper.SpawnProjectileEntity(m_Projectile.GetOwner(), direction, m_Projectile.name,
                spawnPosition, splitCount, m_SpellEntity.SpellBaseData, null, scatter,
                m_UniformScatter, offset);

            Destroy(this);
        }

        /// <summary>
        /// 获取过滤后的Buff列表，排除指定ID的Buff
        /// </summary>
        /// <param name="_activeBuffs">原始Buff列表</param>
        /// <returns>过滤后的Buff列表</returns>
        private List<BuffElement> GetFilteredBuffs(List<BuffElement> _activeBuffs)
        {
            // 方案2：使用LINQ进行高效过滤（推荐）
            var filteredBuffs = new List<BuffElement>(_activeBuffs.Count);
            
            foreach (var buff in _activeBuffs)
            {
                if (buff.ID != c_ExcludedBuffID)
                {
                    filteredBuffs.Add(buff);
                }
            }
            
            return filteredBuffs;
            
            // 方案3：如果需要去重并使用字典，可以使用以下代码：
            /*
            var seenBuffIds = new HashSet<int>();
            var filteredBuffs = new List<BuffElement>();
            
            foreach (var buff in _activeBuffs)
            {
                if (buff.ID != c_ExcludedBuffID && seenBuffIds.Add(buff.ID))
                {
                    filteredBuffs.Add(buff);
                }
            }
            
            return filteredBuffs;
            */
        }
        #endregion
    }
}