using System;
using System.Collections.Generic;
using Battle.Buff;
using Battle.Skill;
using cfg.skill;

public static class BuffFactory
{
    // // 定义一个字典，用于存储 ID 到创建函数的映射
    // private static Dictionary<int, Func<Buff, BuffElement>> _buffCreators;
    //
    // // 静态构造函数，初始化字典
    // static BuffFactory()
    // {
    //     _buffCreators = new Dictionary<int, Func<Buff, BuffElement>>
    //     {
    //         { 100001, CreateNumBuff },
    //         { 100002, CreateSizeBuff }
    //         // 可以在这里添加更多的映射
    //     };
    // }

    // 根据 ID 创建对应类型的 Buff
    public static BuffElement CreateBuff(Buff tbBuff)
    {
        // if (_buffCreators.TryGetValue(tbBuff.Id, out var creator))
        // {
        //     return creator(tbBuff);
        // }

        switch (tbBuff.Id)
        {
            case 20003:
                return new Buff20003(tbBuff.Id);
        }

        // 如果找不到对应的创建函数，可以返回 null 或抛出异常
        return new DefaultBuff(tbBuff.Id);
    }

    // public static NumBuff CreateNumBuff(Buff tbBuff)
    // {
    //     NumBuff buff = new NumBuff();
    //
    //     buff.ID = tbBuff.Id; // ID
    //     buff.Type = ElementType.Buff;// 类型
    //     buff.SType = (int)tbBuff.Stype; // 子类型
    //     buff.ElementName = tbBuff.Name; // 名字
    //     buff.ElementDesc = tbBuff.Desc; // 描述
    //     buff.EffectivePoint = (BuffEffectivePoint)tbBuff.EffectivePoint; // 生效阶段
    //
    //     buff.Num = tbBuff.EmitNumber; // 发射数目
    //
    //     return buff;
    // }
    //
    // public static SizeBuff CreateSizeBuff(Buff tbBuff)
    // {
    //     SizeBuff buff = new SizeBuff();
    //
    //     buff.ID = tbBuff.Id; // ID
    //     buff.Type = ElementType.Buff; // 类型
    //     buff.SType = (int)tbBuff.Stype; // 子类型
    //     buff.ElementName = tbBuff.Name; // 描述
    //     buff.ElementDesc = tbBuff.Desc;  // 描述
    //
    //     buff.SizeMultiplier = tbBuff.EffectSize; // 尺寸
    //     buff.EffectivePoint = (BuffEffectivePoint)tbBuff.EffectivePoint; // 生效阶段
    //
    //
    //     return buff;
    // }

}