using System.Collections.Generic;
using Battle.Component;
using Battle.Skill;
using cfg.skill;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Battle.Utils
{
    public static class BattleHelper
    {
        private static SkillElement CreateSkillElement(int spellId)
        {
            // switch (spellId)
            // {
            //     case 10001:
            //         return new SkillProjectile(spellId);
            //     case 10002:
            //         return new SkillBlackHole(spellId);
            //     case 10004:
            //         return new Spell10004(spellId);
            //     case 10005:
            //         return new Spell10005(spellId);
            //     case 10007:
            //         return new Spell10007(spellId);
            // }

            //DebugUtil.LogError("{0}未找到技能{1}", nameof(CreateSkillElement), spellId);
            return new DefaultSpellElement(spellId);
        }

        /// <summary>
        /// 当前本地玩家添加法术物品
        /// </summary>
        /// <param name="spellId"></param>
        public static void AddSlotElement(int spellId)
        {
            var spellType = DataTableManager.Instance.Tables.TbBuff.GetOrDefault(spellId) is not null
                ? ElementType.Buff
                : ElementType.Skill;
            if (spellType == ElementType.Skill)
            {
                var se = CreateSkillElement(spellId);
                if (se is null)
                {
                    DebugUtil.LogError("未找到技能{0}", spellId);
                    return;
                }

                se.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(se);
            }
            else
            {
                // Buff
                cfg.skill.Buff buff = DataTableManager.Instance.Tables.TbBuff.GetOrDefault(spellId);
                if (buff is null)
                {
                    DebugUtil.LogError("未找到buff{0}", spellId);
                    return;
                }

                // 使用 BuffFactory 创建对应类型的 Buff
                var buffElement = BuffFactory.CreateBuff(buff);
                if (buffElement is null)
                {
                    DebugUtil.LogError("未找到ID为{0}的Buff创建方法", spellId);
                    return;
                }

                buffElement.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(buffElement);
            }
        }


        /// <summary>
        /// Spawns a new object and positions/resizes it
        /// </summary>
        public static GameObject[] SpawnProjectileEntity(GameObject owner,
            Vector3 targetDirection, string entityName,
            Vector3 spawnPosition, int totalCount, SpellBaseData spellBaseData = default,List<BuffElement> buffElements = null, float scatter = 0,
            bool scatterUniform = true, float offset = 0)
        {
            var objectPool = SIXGameManager.Instance.skillPool;
            targetDirection = Vector3.Normalize(targetDirection);
            var results = new GameObject[totalCount];
            for (int i = 0; i < totalCount; i++)
            {
                /// we get the next object in the pool and make sure it's not null
                GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
                // mandatory checks
                if (nextGameObject == null)
                {
                    DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                    return null;
                }

                if (nextGameObject.GetComponent<MMPoolableObject>() == null)
                {
                    DebugUtil.LogError(owner.name +
                                       " is trying to spawn objects that don't have a PoolableObject component.");
                    return null;
                }

                var spellEntity = nextGameObject.GetComponent<SpellEntity>();
                if (spellEntity)
                {
                    spellEntity.Set(spellBaseData, buffElements);
                }

                var damage = nextGameObject.GetComponent<DamageOnTouch>();
                if (damage)
                {
                    damage.MinDamageCaused = spellBaseData.GetValue(SpellAttribute.Damage);
                    damage.MaxDamageCaused = spellBaseData.GetValue(SpellAttribute.Damage);
                }

                nextGameObject.transform.position = spawnPosition;
                Projectile projectile = nextGameObject.GetComponent<Projectile>();
                if (projectile != null)
                {
                    projectile.LifeTime = spellBaseData.GetValue(SpellAttribute.Duration);
                    projectile.Speed = spellBaseData.GetValue(SpellAttribute.Speed);
                    Vector3 _randomSpreadDirection = Vector3.zero;

                    if (scatter == 0)
                    {
                        //do nothing
                    }
                    else if (scatterUniform)
                    {
                        _randomSpreadDirection.z =
                            MMMaths.Remap(i, 0, totalCount - 1, -scatter / 2, scatter / 2);
                    }
                    else if (scatter != 0)
                    {
                        _randomSpreadDirection.z = Random.Range(-scatter / 2, scatter / 2);
                    }

                    Quaternion spread = Quaternion.Euler(_randomSpreadDirection);
                    projectile.SetDirection(spread * targetDirection);
                    nextGameObject.transform.position +=
                        projectile.Direction.normalized * offset;
                }


                nextGameObject.gameObject.SetActive(true);
                if (nextGameObject.GetComponent<MMPoolableObject>() != null)
                {
                    nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
                }
                

                results[i] = nextGameObject;
            }

            return results;
        }


        public static void GenHitGOOnTransform(GameObject owner, string entityName)
        {
            // Check if SIXGameManager instance exists
            if (SIXGameManager.Instance == null)
            {
                DebugUtil.LogError("SIXGameManager.Instance is null");
                return;
            }

            // Check if skillPool is configured
            var objectPool = SIXGameManager.Instance.skillPool;
            if (objectPool == null)
            {
                DebugUtil.LogError("SIXGameManager.Instance.skillPool is null");
                return;
            }

            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);

            // If object not found in pool, try to load from Resources and add to pool
            if (nextGameObject == null)
            {
                nextGameObject = TryAddObjectToPool(objectPool, entityName);
                if (nextGameObject == null)
                {
                    DebugUtil.LogError("{0} doesnt have {1} and couldn't load it from Resources. Please add this object to the skill pool or check if the object exists in Resources folder.", objectPool.name, entityName);
                    return;
                }
            }

            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError(owner.name + " is trying to spawn objects that don't have a PoolableObject component.");
                return;
            }
            nextGameObject.transform.parent = owner.transform;
            nextGameObject.transform.localPosition = Vector3.zero;
            nextGameObject.gameObject.SetActive(true);

            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }

        /// <summary>
        /// Tries to load an object from Resources and add it to the object pool
        /// </summary>
        /// <param name="objectPool">The object pool to add to</param>
        /// <param name="entityName">Name of the entity to load</param>
        /// <returns>The pooled object if successful, null otherwise</returns>
        private static GameObject TryAddObjectToPool(MMMultipleObjectPooler objectPool, string entityName)
        {
            // Try to load from common effect paths
            string[] possiblePaths = {
                $"Prefabs/Skill/{entityName}",           // 添加这个路径，因为 Spell10002Hit 在这里
                $"Prefabs/Effect/Skill/{entityName}",    // 现有的技能效果路径
                $"Resources/Effect/Skill/{entityName}",  // Resources 中的技能效果路径
                $"Effects/{entityName}",
                $"Prefabs/Effects/{entityName}",
                $"Prefabs/Skills/{entityName}",
                $"Skills/{entityName}",
                entityName
            };

            GameObject prefab = null;

            // First try to load from Resources folder
            foreach (string path in possiblePaths)
            {
                prefab = Resources.Load<GameObject>(path);
                if (prefab != null)
                {
                    DebugUtil.LogWarning("Auto-loaded {0} from Resources path: {1}", entityName, path);
                    break;
                }
            }

            // If not found in Resources, try to load from Assets using AssetDatabase (Editor only)
            #if UNITY_EDITOR
            if (prefab == null)
            {
                string[] assetPaths = {
                    $"Assets/Prefabs/Skill/{entityName}.prefab",
                    $"Assets/Prefabs/Effect/Skill/{entityName}.prefab",
                    $"Assets/Resources/Effect/Skill/{entityName}.prefab"
                };

                foreach (string assetPath in assetPaths)
                {
                    prefab = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                    if (prefab != null)
                    {
                        DebugUtil.LogWarning("Auto-loaded {0} from Asset path: {1}", entityName, assetPath);
                        break;
                    }
                }
            }
            #endif

            if (prefab == null)
            {
                return null;
            }

            // Add to the multiple object pooler
            var poolObject = new MMMultipleObjectPoolerObject
            {
                GameObjectToPool = prefab,
                PoolSize = 5, // Default pool size
                PoolCanExpand = true,
                Enabled = true
            };

            // Add to the pool list
            if (objectPool.Pool == null)
            {
                objectPool.Pool = new List<MMMultipleObjectPoolerObject>();
            }
            objectPool.Pool.Add(poolObject);

            // Fill the pool with this new object
            objectPool.FillObjectPool();

            // Try to get the object again
            return objectPool.GetPooledGameObjectOfType(entityName);
        }

        public static void GenLinkGo(GameObject start, GameObject end, string entityName)
        {
            entityName = $"{entityName}Link";
            var objectPool = SIXGameManager.Instance.skillPool;
            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
            if (nextGameObject == null)
            {
                DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                return;
            }
            
            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError($" is trying to spawn objects{nextGameObject.name} that don't have a PoolableObject component.");
                return;
            }

            var linkComponent = nextGameObject.GetComponent<LinkComponent>();
            if (!linkComponent)
            {
                DebugUtil.LogError($" {nextGameObject.name} that don't have a LinkComponent component.");
                return;
            }
            linkComponent.SetLink(start.transform, end.transform);
            nextGameObject.SetActive(true);
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }
        
        
        public static void GenLinkGo(Vector3 start, Vector3 end, string entityName)
        {
            entityName = $"{entityName}Link";
            var objectPool = SIXGameManager.Instance.skillPool;
            GameObject nextGameObject = objectPool.GetPooledGameObjectOfType(entityName);
            if (nextGameObject == null)
            {
                DebugUtil.LogError("{0} doesnt have {1}", objectPool.name, entityName);
                return;
            }
            
            if (nextGameObject.GetComponent<MMPoolableObject>() == null)
            {
                DebugUtil.LogError($" is trying to spawn objects{nextGameObject.name} that don't have a PoolableObject component.");
                return;
            }

            var linkComponent = nextGameObject.GetComponent<LinkComponent>();
            if (!linkComponent)
            {
                DebugUtil.LogError($" {nextGameObject.name} that don't have a LinkComponent component.");
                return;
            }
            linkComponent.SetLinkPosition(start, end);
            nextGameObject.SetActive(true);
            if (nextGameObject.GetComponent<MMPoolableObject>() != null)
            {
                nextGameObject.GetComponent<MMPoolableObject>().TriggerOnSpawnComplete();
            }
        }
    }
}