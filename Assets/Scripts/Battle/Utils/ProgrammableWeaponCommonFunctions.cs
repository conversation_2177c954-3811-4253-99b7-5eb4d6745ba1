using System.Collections.Generic;
using MoreMountains.TopDownEngine;
using UnityEngine;

public class ProgrammableWeaponCommonFunctions
{
    public static GameObject FindNearestCharacter(Vector3 position, float range = float.MaxValue, string tag = "Enemy", HashSet<GameObject> excludeGameObject = null)
    {
        GameObject[] characters = GameObject.FindGameObjectsWithTag(tag);
        GameObject nearestCharacter = null;
        float minDistance = float.MaxValue;

        foreach (GameObject character in characters)
        {
            if (excludeGameObject is not null && excludeGameObject.Contains(character))
            {
                continue;
            }
            
            // Check if the enemy is alive
            var characterHealth = character.GetComponent<Health>(); // Replace with the actual component that checks health
            if (characterHealth != null && characterHealth.CurrentHealth > 0) // Replace IsAlive() with the actual method
            {
                float distance = Vector3.Distance(position, character.transform.position);

                if (distance < minDistance && distance <= range)
                {
                    minDistance = distance;
                    nearestCharacter = character;
                }
            }
        }

        return nearestCharacter;
    }


    /// <summary>
    /// 查找范围内实体, todo 优化
    /// </summary>
    /// <param name="position"></param>
    /// <param name="name"></param>
    /// <param name="range"></param>
    /// <returns></returns>
    public static GameObject[] FindRangeEntity(Vector3 position, string name,float range = float.MaxValue)
    {
        var results = new List<GameObject>();
        
        //临时这样处理，后面要加实体管理、加阵营区分优化
        GameObject[] effects = GameObject.FindGameObjectsWithTag("Projectile");
        foreach (GameObject go in effects)
        {
            if (name == null || go.name == name)
            {
                if (Vector3.Distance(position, go.transform.position) <= range)
                {
                    results.Add(go);
                }
            }
        }

        return results.ToArray();

    }
}