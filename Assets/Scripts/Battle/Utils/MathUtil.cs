

using UnityEngine;

namespace Battle.Utils
{
    public static class MathUtil
    {
        public static Vector3 RandomDirectionEuler()
        {
           
            float randomX = Random.Range(0f, 360f);
            float randomY = Random.Range(0f, 360f);
            float randomZ = Random.Range(0f, 360f);
            return Quaternion.Euler(randomX, randomY, randomZ) * Vector3.forward;
        }
    }
}