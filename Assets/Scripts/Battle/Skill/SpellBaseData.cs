using System.Collections.Generic;
using cfg;
using cfg.skill;
using Unity.VisualScripting;

namespace Battle.Skill
{
    /// <summary>
    /// 可能被buff影响的基础法术数据
    /// </summary>
    public struct SpellBaseData
    {
        private Dictionary<int, float> _baseData;
        private SpellLevel _cfg;
        public SpellBaseData(SpellLevel cfg)
        {
            _baseData = new Dictionary<int, float>(cfg.BaseData);
            _cfg = cfg;
        }

        public void Reset()
        {
            _baseData.Clear();
            foreach (var keyValuePair in _cfg.BaseData)
            {
                _baseData.Add(keyValuePair.Key, keyValuePair.Value);
            }
        }
        public void ApplyBuff(BuffEffectPair[] buffEffectPairs, BuffEffectivePoint point)
        {
            
            foreach (var buffEffectPair in buffEffectPairs)
            {
                if (buffEffectPair.EffectPoint != point)
                {
                    continue;
                }
                float value = _baseData.GetValueOrDefault(buffEffectPair.Id, 0);
                switch (buffEffectPair.ValueOperator)
                {
                    case BuffEffectOperator.ADD:
                        value += buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.MUL:
                        value *= buffEffectPair.Value;
                        break;
                    case BuffEffectOperator.SET:
                        value = buffEffectPair.Value;
                        break;
                }
            
                _baseData[buffEffectPair.Id] = value;
            }
        }

        public float GetValue(SpellAttribute spellAttribute)
        {
            var id = (int)spellAttribute;
            return _baseData.GetValueOrDefault(id, 0);
        }
    }
}