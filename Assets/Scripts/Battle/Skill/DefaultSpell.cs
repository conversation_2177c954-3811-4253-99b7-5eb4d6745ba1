using System;
using System.Collections.Generic;
using System.Linq;
using Battle.Component;
using Battle.Utils;
using cfg.skill;
using MoreMountains.Tools;
using Unity.VisualScripting;
using UnityEngine;

namespace Battle.Skill
{
    /// <summary>
    /// 滚石
    /// </summary>
    public sealed class DefaultSpellElement : SkillElement
    {
        private MMMultipleObjectPooler _objectPooler;
        private GameObject _owner;
        private List<BuffElement> _activeBuffs;
        private SpellLevel _skillLevelCfg => DataTableManager.Instance.Tables.TbSpellLevel.Get(ID, 1);
        private Spell _skillCfg => DataTableManager.Instance.Tables.TbSpell.Get(ID);

        private SpellBaseData _spellBaseData;
        public override int Consume => (int)_spellBaseData.GetValue(SpellAttribute.Consume); 
        
        public DefaultSpellElement(int id)
        {
            ID = id;
            Type = ElementType.Skill;
            ElementName = $"法术：{_skillCfg.Name}";
            _spellBaseData = new SpellBaseData(_skillLevelCfg);
        }
        

        public override void ExecuteSkill(GameObject owner, Transform spawnPoint, List<BuffElement> activeBuffs)
        {
            var target = ProgrammableWeaponCommonFunctions.FindNearestCharacter(spawnPoint.position);
            var direction = target
                ? target.transform.position - spawnPoint.position
                : spawnPoint.position - owner.transform.position;

            _owner = owner;
            _spellBaseData.Reset();
            foreach (var buffElement in _activeBuffs)
            {
                _spellBaseData.ApplyBuff(buffElement.BuffLevelCfg.Effects, BuffEffectivePoint.BeforeBirth);
            }
            
            //ApplyBuffs(this);
            var results = BattleHelper.SpawnProjectileEntity(
                _owner,
                direction,
                $"Spell{_skillCfg.Id.ToString()}",
                spawnPoint.position,
                (int)_spellBaseData.GetValue(SpellAttribute.SpawnNum),
                _spellBaseData,
                new List<BuffElement>(_activeBuffs),
                (int)_spellBaseData.GetValue(SpellAttribute.ScatteringDegree),
                false
            );



            foreach (var gameObject in results)
            {
                var refractionNum = (int)_spellBaseData.GetValue(SpellAttribute.RefractionNum);
                var refractionComponent = refractionNum > 0
                    ? gameObject.GetOrAddComponent<RefractionComponent>()
                    : gameObject.GetComponent<RefractionComponent>();
                if (refractionComponent)
                {
                    refractionComponent.Init(new RefractionData()
                    {
                        RefractionCount = refractionNum,
                        RefractionRadius = _skillLevelCfg.RefractionRadius,
                    });
                }

                ApplyBuffs(gameObject);
            }
        }

        public override void SetObjectPool(MMMultipleObjectPooler mObjectPooler)
        {
            base.SetObjectPool(mObjectPooler);
            _objectPooler = mObjectPooler;
        }

        public override void SaveBuffs(List<BuffElement> activeBuffs)
        {
            base.SaveBuffs(activeBuffs);
            _activeBuffs = activeBuffs;
        }

        private void ApplyBuffs(GameObject skillGameObject)
        {
            foreach (var buff in _activeBuffs)
            {
                buff.ApplyBuff(skillGameObject);
            }
        }

        private void ApplyBuffs(SkillElement skillElement)
        {
            foreach (var buff in _activeBuffs)
            {
                buff.ApplyBuff(skillElement);
            }
        }
    }
}