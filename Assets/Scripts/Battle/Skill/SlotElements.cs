using MoreMountains.TopDownEngine;
using UnityEngine;

public enum ElementType
{
    Skill,
    Buff
}

public class SlotElement
{
    public int ID;
    /// <summary>
    /// 元素类型, 分Skill、Buff 两种
    /// </summary>
    public ElementType Type;
    public string ElementName; // 名字
    public string ElementDesc; // 描述
    // public Sprite ElementIcon;
    /// <summary>
    /// 槽位序号
    /// </summary>
    public int SlotIndex = -1;
    /// <summary>
    /// 槽位类型 1: 表示在法杖中, 2: 表示在魔法书中, 0 为默认值，表示暂未处理
    /// </summary>
    public int SlotType = 0;
    
    public virtual void Initialize() { }

    public virtual SlotElement DeepCopy(){ return null; }

    /// <summary>
    /// 法力消耗
    /// </summary>
    public virtual int Consume => 0;
}
