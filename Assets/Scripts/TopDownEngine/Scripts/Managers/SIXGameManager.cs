using cfg.bonus;
using cfg.role;
using cfg.skill;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using Battle.Utils;
using UnityEngine;

public class SIXGameManager : <PERSON><PERSON><PERSON><PERSON>, MMEventListener<CharacterDeathEvent>
{
    protected Dictionary<MonsterKey, bool> MonsterSurvivalStatus = new Dictionary<MonsterKey, bool>();

    public override void ResetMonsterSurvalStatus()
    {
        // 初始化当前怪物的生存情况
        MonsterSurvivalStatus.Clear();
        int mapID = PlayerData.GetInstance().LevelData.MapID;
        cfg.map.MapConfiguration mapCfg = DataTableManager.Instance.Tables.TbMap.Get(mapID);
        if (mapCfg.MonsterRefreshesData.Count > 0)
        {
            var monsters = mapCfg.MonsterRefreshesData[PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex - 1];
            foreach (var m in monsters)
            {
                MonsterKey key = new MonsterKey(m.MonsterId, m.Location);
                if (!MonsterSurvivalStatus.ContainsKey(key))
                {
                    MonsterSurvivalStatus.Add(key, true);
                }
                else
                {
                    MonsterSurvivalStatus[key] = true;
                }
            }
        }
    }

    protected override void Start()
    {
        base.Start();
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        this.MMEventStartListening<CharacterDeathEvent>();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        this.MMEventStopListening<CharacterDeathEvent>();
    }

    public void OnMMEvent(CharacterDeathEvent eventType)
    {
        if (eventType.Type == CharacterType.Monster)
        {
            MonsterKey monsterKey = (MonsterKey)eventType.Data["monster"];
            Vector3 vec3 = (Vector3)eventType.Data["pos"];
            MonsterSurvivalStatus[monsterKey] = false;
            DealingWithMonsterDrops(monsterKey, vec3);

            if (!AreAllMonstersDefeated())
            {
                // 若还有怪物存活， 不做任何处理
                return;
            }

            int mapID = PlayerData.GetInstance().LevelData.MapID;
            cfg.map.MapConfiguration mapCfg = DataTableManager.Instance.Tables.TbMap.Get(mapID);
            if (PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex >= mapCfg.MonsterRefreshesData.Count)
            {
                // 关卡掉落
                DealingWithSectionDrops(vec3);

                var (section, level) = Formula.GetNextSection();
                if (section == 0 && level == 0)
                {
                    Debug.Log("游戏已通关!");
                }
                else
                {
                    var (nextSection, nextLevel) = Formula.GetNextSection();
                    cfg.section.SectionConfiguration curScf = DataTableManager.Instance.Tables.TbSection.Get(section: PlayerData.GetInstance().CurSection, level: PlayerData.GetInstance().CurLevel);
                    cfg.section.SectionConfiguration nextScf = DataTableManager.Instance.Tables.TbSection.Get(section: nextSection, level: nextLevel);
                    List<int> randomMapIds = MMSceneLoadingManager.GetRandomScenesWithoutReplacement(nextScf.RandomMapLibrary, curScf.MapDoorHangingPoint.Count);

                    List<int> bonusesModeList = Formula.GetRandomElementsBasedOnWeights(nextScf.RandomBonusMode, count: randomMapIds.Count);
                    PlayerData.GetInstance().LevelData.RandomBonusModeList = bonusesModeList;
                    int index = 0;
                    foreach (var door in SIXLevelManager.Current.PointsOfDoors)
                    {
                        if (!door.gameObject.activeSelf)
                            continue;

                        int mapId = randomMapIds[index];
                        int bonusModeId = bonusesModeList[index];
                        int bonusType = DataTableManager.Instance.Tables.TbBonus.Get(bonusModeId).Mode;
                        string resPath = Formula.GetDoorRes(bonusType);
                        door.GetComponent<Portal>().NextMapID = mapId;
                        door.GetComponent<Portal>().BonusMode = bonusModeId;
                        door.Find("doorclose").gameObject.SetActive(false);
                        Transform bonusTrans = door.Find("bonus");
                        bonusTrans.gameObject.SetActive(true);
                        GameObject item = Resources.Load<GameObject>(resPath);
                        GameObject go = Instantiate(item, bonusTrans.position, bonusTrans.rotation);
                        go.transform.SetParent(bonusTrans);
                        go.transform.localScale = Vector3.one;
                        index++;
                    }
                    
                }
                //Debug.Log("游戏已通关!");
            }
            else
            {
                // 刷新下一波
                PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex += 1;
                ResetMonsterSurvalStatus(); // 重新刷新怪物存活情况

                var monsters = mapCfg.MonsterRefreshesData[PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex - 1];
                foreach (var m in monsters)
                {
                    cfg.role.Monster monster = DataTableManager.Instance.Tables.TbMonster.Get(m.MonsterId);
                    GameObject monsterPrefab = Resources.Load<GameObject>(monster.ResourceName);
                    if (monsterPrefab != null)
                    {
                        // Check if the specific location index is valid
                        bool validLocationIndex = m.Location > 0 && m.Location <= LevelManager.Instance.PointsOfEntry.Length;
                        var locationTransform = validLocationIndex ? LevelManager.Instance.PointsOfEntry[m.Location - 1] : LevelManager.Instance.InitialSpawnPoint.transform;

                        if (!validLocationIndex)
                        {
                            Debug.LogWarning($"Monster location index {m.Location} is out of range. Using initial spawn point instead.");
                        }
                        GameObject go = Instantiate(monsterPrefab, locationTransform.position, locationTransform.rotation);
                        go.GetComponent<Monster>().MonsterKey = new MonsterKey(m.MonsterId, m.Location); // 怪物在当前关卡的索引
                    }
                    else
                    {
                        Debug.LogError("Monster prefab not found at path: " + monster.ResourceName);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 处理关卡掉落
    /// </summary>
    /// <param name="vec3"></param>
    /// <exception cref="NotImplementedException"></exception>
    private void DealingWithSectionDrops(Vector3 vec)
    {
        int bonusID = Formula.GetIDFromBonusModeTable(PlayerData.GetInstance().LevelData.BonusMode);
        cfg.drop.DropItem dropItem = DataTableManager.Instance.Tables.TbDrop.Get(bonusID);
        List<Tuple<int, int>>  items = Formula.GetRandomItemBasedOnDropTb(dropItem.Id, type: 2);
        // List<cfg.RandomItem> items = Formula.GetRandomItemBasedOnWeights(dropItem.RandomDropItem, count: 1);
        foreach (var item in items)
        {
            for (int i = 0; i < item.Item2; i++)
            {
                cfg.item.Item dpitem = DataTableManager.Instance.Tables.TbItems.Get(item.Item1);

                var prefabPath = dpitem.PrefabPath;
                //todo 后面类型多了需要枚举
                if (dpitem.Type == 2 || dpitem.Type == 3)
                {
                    prefabPath = BattleConstSetting.SkillItemScenePrefabPath;
                }
                GameObject itemPrefab = Resources.Load<GameObject>(prefabPath);
                
                if (itemPrefab != null)
                {
                    float maxRadius = 2.0f;
                    Vector2 randomOffset = UnityEngine.Random.insideUnitCircle * maxRadius;
                    Vector3 spawnPosition = vec + new Vector3(randomOffset.x, randomOffset.y, 0);

                    // Instantiate the item
                    GameObject go = Instantiate(itemPrefab, spawnPosition, Quaternion.identity);
                    go.GetComponent<BaseDropItem>().ItemID = item.Item1; // 设置掉落物品ID
                }
                else
                {
                    Debug.LogError($"Item prefab not found for ItemId: {item.Item1}, PrefabPath: {dpitem.PrefabPath}");
                }
            }
        }
    }

    /// <summary>
    /// 处理怪物掉落
    /// </summary>
    /// <param name="monsterKey"></param>
    /// <param name="vec"></param>
    private void DealingWithMonsterDrops(MonsterKey monsterKey, Vector3 vec)
    {
        cfg.role.Monster monster = DataTableManager.Instance.Tables.TbMonster.Get(monsterKey.ID);
        cfg.drop.DropItem dropItem = DataTableManager.Instance.Tables.TbDrop.Get(monster.DropItem);
        List<Tuple<int, int>> items = Formula.GetRandomItemBasedOnDropTb(dropItem.Id, type: 2);
        // List<cfg.RandomItem>  items = Formula.GetRandomItemBasedOnWeights(dropItem.RandomDropItem, count: 1);
        foreach (var item in items)
        {
            for (int i = 0; i < item.Item2; i++)
            {
                cfg.item.Item dpitem = DataTableManager.Instance.Tables.TbItems.Get(item.Item1);
                GameObject itemPrefab = Resources.Load<GameObject>(dpitem.PrefabPath);
                if (itemPrefab != null)
                {
                    float radius = 0.5f; // Radius of distribution circle
                    float angle = (360f / item.Item2) * i; // Evenly space items in a circle
                    Vector3 offset = Quaternion.Euler(0, 0, angle) * Vector3.right * radius;
                    Vector3 spawnPosition = vec + offset;

                    // Instantiate the item
                    Instantiate(itemPrefab, spawnPosition, Quaternion.identity);
                }
                else
                {
                    Debug.LogError($"Item prefab not found for ItemId: {item.Item1}");
                }
            }
        }

    }

    /// <summary>
    /// 是否所有怪物都被击败
    /// </summary>
    /// <returns></returns>
    protected bool AreAllMonstersDefeated()
    {
        foreach (var status in MonsterSurvivalStatus.Values)
        {
            if (status)
            {
                return false;
            }
        }
        return true;
    }
}

