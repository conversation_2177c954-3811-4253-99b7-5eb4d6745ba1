using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using System.Collections.Generic;
using System.Data;
using UnityEngine;
using UnityEngine.Analytics;
using UnityEngine.SceneManagement;
using static MoreMountains.Tools.MMSceneLoadingManager;

public class SIXLevelManager : LevelManager, MMEventListener<MoreMountains.Tools.MMSceneLoadingManager.LoadingSceneEvent>
{
    [SerializeField]
    private bool showDebugIcons = true;

    protected List<string> PreloadGameObjects = new List<string>();

    protected override void OnEnable()
    {
        base.OnEnable();
        this.MMEventStartListening<LoadingSceneEvent>();

        // 检查 PointsOfDoors 是否已赋值
        if (PointsOfDoors == null || PointsOfDoors.Length == 0)
        {
            Debug.LogWarning("PointsOfDoors 数组未设置或为空。请在 Inspector 中为 SIXLevelManager 组件设置门的挂点。");
            return;
        }

        int section = PlayerData.GetInstance().CurSection;
        int level = PlayerData.GetInstance().CurLevel;
        cfg.section.SectionConfiguration scf = DataTableManager.Instance.Tables.TbSection.Get(section: section, level: level);

        // 首先禁用所有门
        foreach (var door in PointsOfDoors)
        {
            if (door != null)
            {
                door.gameObject.SetActive(false);
            }
        }

        // 根据配置激活指定的门
        foreach (var hp in scf.MapDoorHangingPoint)
        {
            if (hp > PointsOfDoors.Length)
            {
                Debug.LogError(string.Format("【章节.xlsx】中{0}-{1}的【地图门挂点】填写序号超过门的序号!", section, level));
                continue;
            }

            if (PointsOfDoors[hp - 1] != null)
            {
                PointsOfDoors[hp - 1].gameObject.SetActive(true);

                Transform doorCloseTransform = PointsOfDoors[hp - 1].transform.Find("doorclose");
                if (doorCloseTransform != null)
                {
                    doorCloseTransform.gameObject.SetActive(true);
                }

                Transform bonusTransform = PointsOfDoors[hp - 1].transform.Find("bonus");
                if (bonusTransform != null)
                {
                    bonusTransform.gameObject.SetActive(false);
                }
            }
            else
            {
                Debug.LogError(string.Format("PointsOfDoors[{0}] 为空，请检查 Inspector 中的设置。", hp - 1));
            }
        }
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        this.MMEventStopListening<LoadingSceneEvent>();
    }

    protected override void SpawnSingleCharacter()
    {
        if (InitialSpawnPoint != null)
        {
            InitialSpawnPoint.SpawnPlayer(Players[0]);
            TopDownEngineEvent.Trigger(TopDownEngineEventTypes.SpawnComplete, Players[0]);
            return;
        }
    }

    protected override void InstantiateGameData()
    {
        base.InstantiateGameData();
        PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex = 1; // 初始默认 怪物刷新波次
        SIXGameManager.Instance.ResetMonsterSurvalStatus();
    }

    protected override void InstantiateMonster()
    {
        int mapID = PlayerData.GetInstance().LevelData.MapID;
        cfg.map.MapConfiguration mapCfg = DataTableManager.Instance.Tables.TbMap.Get(mapID);
        if (mapCfg.MonsterRefreshesData.Count > 0)
        {
            var monsters = mapCfg.MonsterRefreshesData[PlayerData.GetInstance().LevelData.CurMonsterRefreshIndex - 1];
            
            foreach (var m in monsters)
            {
                cfg.role.Monster monster = DataTableManager.Instance.Tables.TbMonster.Get(m.MonsterId);
                GameObject monsterPrefab = Resources.Load<GameObject>(monster.ResourceName);
                if (monsterPrefab != null)
                {
                    // Check if the specific location index is valid
                    bool validLocationIndex = m.Location > 0 && m.Location <= LevelManager.Instance.PointsOfEntry.Length;
                    var locationTransform = validLocationIndex ? PointsOfEntry[m.Location - 1] : InitialSpawnPoint.transform;
                    
                    if (!validLocationIndex)
                    {
                        Debug.LogWarning($"Monster location index {m.Location} is out of range. Using initial spawn point instead.");
                    }

                    // Play spawn effect first, then instantiate the monster
                    GameObject go = Instantiate(monsterPrefab, locationTransform.position, locationTransform.rotation);
                    go.GetComponent<Monster>().MonsterKey = new MonsterKey(m.MonsterId, m.Location); // 怪物在当前关卡的索引
                }
                else
                {
                    Debug.LogError("Monster prefab not found at path: " + monster.ResourceName);
                }
            }
        }
    }

    /// <summary>
    /// 场景预加载工作
    /// </summary>
    protected override void PreloadWorks()
    {
        PreloadGameObjects.Add("SIXGameManager");
        PreloadGameObjects.Add("SoundManager");
        PreloadGameObjects.Add("DataTableManager");

        if (Formula.isInLevelRoom())
        {
            PrepareForLevelRoom();
        }
    }

    /// <summary>
    /// 进入关卡时的准备工作
    /// </summary>
    protected void PrepareForLevelRoom()
    {
        foreach (string prefabName in PreloadGameObjects)
        {
            // 判断场景内是否存在名称为GameManager的GameObject
            if (GameObject.Find(prefabName) == null && !SIXGameManager.HasInstance)
            {
                // 若不存在，则从Resources中加载
                GameObject gameManagerPrefab = Resources.Load<GameObject>(string.Format("Managers/{0}", prefabName));
                if (gameManagerPrefab != null)
                {
                    Instantiate(gameManagerPrefab);
                }
                else
                {
                    Debug.LogError(string.Format("{0} prefab not found in Resources folder", prefabName));
                }
            }
        }
    }

    public void OnMMEvent(LoadingSceneEvent eventType)
    {
        //if (eventType.Status == MMSceneLoadingManager.LoadingStatus.LoadTransitionComplete)
        //{
        //    if (Formula.isInLevelRoom())
        //    { // 是否在关卡内
        //        PrepareForLevelRoom();
        //    }
            
        //}
    }

    private void OnDrawGizmos()
    {
        if (!showDebugIcons) return;

        Scene currentScene = SceneManager.GetActiveScene();
        GameObject[] rootObjects = currentScene.GetRootGameObjects();

        Gizmos.color = Color.red;

        foreach (GameObject obj in rootObjects)
        {
            FindMonsterSpawns(obj.transform);
        }
    }

    private void FindMonsterSpawns(Transform parent)
    {
        if (parent.name.ToLower().StartsWith("monsterspawn"))
        {
            Gizmos.DrawWireSphere(parent.position, 0.5f);
            
            // 提取序号
            string name = parent.name.ToLower();
            string indexStr = "";
            
            // 从名称中提取数字部分
            for (int i = "monsterspawn".Length; i < name.Length; i++)
            {
                if (char.IsDigit(name[i]))
                {
                    indexStr += name[i];
                }
            }
            
            // 如果找到序号，则显示
            if (!string.IsNullOrEmpty(indexStr))
            {
#if UNITY_EDITOR
                UnityEditor.Handles.color = Color.yellow;
                GUIStyle style = new GUIStyle();
                style.normal.textColor = Color.yellow;
                style.fontSize = 20;
                style.fontStyle = FontStyle.Bold;
                style.alignment = TextAnchor.MiddleCenter;
                UnityEditor.Handles.Label(parent.position + Vector3.up * 0.7f, indexStr, style);
#endif
            }
        }

        for (int i = 0; i < parent.childCount; i++)
        {
            FindMonsterSpawns(parent.transform.GetChild(i));
        }
    }
}