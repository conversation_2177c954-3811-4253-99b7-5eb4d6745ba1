using MoreMountains.TopDownEngine;
using UnityEngine;
using System.Collections.Generic;
using System;
using MoreMountains.Tools;
using MoreMountains.Feedbacks;
using System.Collections;

public class ExtendedWeaponSystem : Weapon, MMEventListener<TopDownEngineEvent>
{

    [MMInspectorGroup("Projectiles", true, 22)]
    /// the offset position at which the projectile will spawn
    [Tooltip("the offset position at which the projectile will spawn")]
    public Vector3 ProjectileSpawnOffset = Vector3.zero;

    /// the object pooler used to spawn projectiles, if left empty, this component will try to find one on its game object
    [Tooltip("the object pooler used to spawn projectiles, if left empty, this component will try to find one on its game object")]
    public MMObjectPooler ObjectPooler;

    protected List<SlotElement> _enhancedSlots = new List<SlotElement>();

    [Header("Spawn Feedbacks")]
    public List<MMFeedbacks> SpawnFeedbacks = new List<MMFeedbacks>();

    protected float _elapsedTime = 0f; // Track elapsed time

    protected Vector3 _flippedProjectileSpawnOffset;
    protected Vector3 _randomSpreadDirection;
    protected bool _poolInitialized = false;
    protected Transform _projectileSpawnTransform;
    protected int _spawnArrayIndex = 0;

    public enum MagicStaffCastStatus
    {
        Ready,
        OnCastInternal,
        OnCooldown
    }

    protected MagicStaffCastStatus _magicStaffCastStatus = MagicStaffCastStatus.Ready;

    // 刷新列表，只读属性
    public IReadOnlyList<SlotElement> EnhancedSlots => _enhancedSlots.AsReadOnly();

    // 当前释放法术的槽位索引
    protected int _currentSkillSlotIndex = -1;

    public override void Initialization()
    {
        base.Initialization();
        if (!_poolInitialized)
        {
            if (ObjectPooler == null)
            {
                ObjectPooler = GetComponent<MMObjectPooler>();
            }
            if (ObjectPooler == null)
            {
                Debug.LogWarning(this.name + " : no object pooler (simple or multiple) is attached to this Projectile Weapon, it won't be able to shoot anything.");
                return;
            }
            if (FlipWeaponOnCharacterFlip)
            {
                _flippedProjectileSpawnOffset = ProjectileSpawnOffset;
                _flippedProjectileSpawnOffset.y = -_flippedProjectileSpawnOffset.y;
            }
            _poolInitialized = true;
        }
    }

    public void OnMMEvent(TopDownEngineEvent engineEvent)
    {
        switch (engineEvent.EventType)
        {
            case TopDownEngineEventTypes.LevelStart:
                _poolInitialized = false;
                Initialization();
                break;
        }
    }

    /// <summary>
    /// This method is in charge of playing feedbacks on projectile spawn
    /// </summary>
    protected virtual void PlaySpawnFeedbacks()
    {
        if (SpawnFeedbacks.Count > 0)
        {
            SpawnFeedbacks[_spawnArrayIndex]?.PlayFeedbacks();
        }

    }

    // 重写武器使用方法
    public override void WeaponUse()
    {
        //if (Owner != null)
        //{
        //    if (Owner.CharacterType == MoreMountains.TopDownEngine.Character.CharacterTypes.Player)
        //    {
                //currentMagicValue -= perConsumeMagicValue;
                //currentMagicValue = Math.Min(maxMagicValue, currentMagicValue);
                //if (GUIManager.HasInstance)
                //{
                //    GUIManager.Instance.UpdateMagicBar(currentMagicValue, minMagicValue, maxMagicValue, Owner.PlayerID);
                //}
        //    }
        //}        
    }

    protected override void RevertProperties()
    {
        _elapsedTime += Time.deltaTime; // Accumulate time

        if (_elapsedTime >= 1f) // Check if a second has passed
        {
            //currentMagicValue += recoverMagicValue;
            //currentMagicValue = Mathf.Min(maxMagicValue, currentMagicValue); // Ensure it doesn't exceed maxMagicValue

            //if (GUIManager.HasInstance)
            //{
            //    GUIManager.Instance.UpdateMagicBar(currentMagicValue, minMagicValue, maxMagicValue, Owner.PlayerID);
            //}

            _elapsedTime = 0f; // Reset elapsed time
        }

    }

    // 技能释放逻辑
    protected virtual void ExecuteSkillChain()
    {
        List<BuffElement> buffs = new List<BuffElement>();
        Dictionary<SkillElement, List<BuffElement>> skills = new Dictionary<SkillElement, List<BuffElement>>();

        foreach (var slot in _enhancedSlots)
        {
            if (slot.Type == ElementType.Buff)
            {
                // 添加持续性buff
                buffs.Add(slot as BuffElement);
            }
            else if (slot.Type == ElementType.Skill)
            {
                // 添加持续性技能
                skills.Add(slot as SkillElement, new List<BuffElement>(buffs.ToArray()));
                // buffs.Clear();
            }
        }

        if (CheckIfTheCastingConditionsAreMet())
        {
            DeductingSkillConsumption();
            refreshViewDisplay();
            StartCoroutine(ExecuteSkillsWithIntervals(skills));
        }
    }

    protected virtual void refreshViewDisplay()
    {
        // Refresh view display
    }

    protected virtual void DeductingSkillConsumption()
    {
        // Deducting skill consumption
    }

    protected virtual IEnumerator ExecuteSkillsWithIntervals(Dictionary<SkillElement, List<BuffElement>> skills)
    {

        foreach (KeyValuePair<SkillElement, List<BuffElement>> kv in skills)
        {
            if (_currentSkillSlotIndex != -1 && kv.Key.SlotIndex < _currentSkillSlotIndex)
            {
                continue;
            }

            _currentSkillSlotIndex = kv.Key.SlotIndex; // 当前释放法术的槽位索引
            if (!SingleCastingConditionInspection())
            {
                ResetMagicStaffCastStatus();
                yield break;
            }
            
            HandlIndividualSkillConsumption(); // 技能消耗

            DateTime now = DateTime.Now;
            Debug.Log("Execute skill: " + kv.Key.ElementName + " Time: " + (now.Second + now.Millisecond / 1000.0).ToString("F2"));
            ActivateSkill(kv.Key, kv.Value);

            _currentSkillSlotIndex = -1; // 重置当前释放法术的槽位索引
            yield return StartCoroutine(CastIntervalRoutine());
        }
        yield return StartCoroutine(CooldownRoutine());
        _magicStaffCastStatus = MagicStaffCastStatus.Ready;
    }

    protected virtual void ResetMagicStaffCastStatus()
    {

    }

    protected virtual void HandlIndividualSkillConsumption()
    {

    }

    protected virtual bool CheckIfTheCastingConditionsAreMet()
    {
        return true;
    }

    protected virtual bool SingleCastingConditionInspection()
    {
        return true;
    }

    protected virtual System.Collections.IEnumerator CastIntervalRoutine()
    { 
        yield return new WaitForSeconds(0);
    }

    protected virtual System.Collections.IEnumerator CooldownRoutine()
    {
        yield return new WaitForSeconds(0);
    }

    protected void ActivateSkill(SkillElement skill, List<BuffElement> buffs)
    {
        skill.SaveBuffs(buffs);
        skill.SetObjectPool((MMMultipleObjectPooler)ObjectPooler);
        skill.ExecuteSkill(gameObject, WeaponUseTransform, null);
    }

    /// <summary>
    /// On enable we start listening for events
    /// </summary>
    protected virtual void OnEnable()
    {
        this.MMEventStartListening<TopDownEngineEvent>();
    }

    /// <summary>
    /// On disable we stop listening for events
    /// </summary>
    protected virtual void OnDisable()
    {
        this.MMEventStopListening<TopDownEngineEvent>();
    }

}