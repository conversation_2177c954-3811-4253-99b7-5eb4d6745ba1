using System;
using System.Collections.Generic;
using Battle.Core;
using Battle.Skill;
using cfg.skill;
using MoreMountains.TopDownEngine;
using UnityEngine;
using UnityEngine.Serialization;

namespace Battle.Component
{
    [RequireComponent(typeof(SpellEntity))]
    public class TargetProjectTileExt : Projectile
    {
        [SerializeField]
        private TargetFindType targetFindType = TargetFindType.Nearest;
        
        [SerializeField]
        private float activeLifeTime = 0;
        [SerializeField]
        private float traceTargetSpeed = 10;
        [SerializeField]
        private float traceTargetAngleSpeed = 300;
        [SerializeField]
        private float findTargetRange = 5;
        
        
        private GameObject _target;

        private SpellEntity _spellEntity;

        public RaycastHit2D hitResult;
        protected override void Awake()
        {
            base.Awake();
            _spellEntity = GetComponent<SpellEntity>();
        }
        
        public bool TryGetTarget()
        {
            if (_target == null)
            {
                if (targetFindType == TargetFindType.Nearest)
                {
                    _target = ProgrammableWeaponCommonFunctions.FindNearestCharacter(transform.position, findTargetRange);
                }
                else if(targetFindType == TargetFindType.OtherHit)
                {
                    //不做事，当其他物体发生碰撞时自动赋值
                }
                else if (targetFindType == TargetFindType.OverlapNearest)
                {
                    var range = _spellEntity.SpellBaseData.GetValue(SpellAttribute.Range);
                    #if UNITY_EDITOR
                    Debug.DrawRay(gameObject.transform.position, Direction * range, Color.blue, 1);
                    #endif
                    hitResult = Physics2D.Raycast(
                        new Vector2(gameObject.transform.position.x, gameObject.transform.position.y), 
                        new Vector2(Direction.x, Direction.y), range, _damageOnTouch.TargetLayerMask
                    );  
                    
                    if (hitResult.collider != null)
                    {
                        _target = hitResult.collider.gameObject;
                        transform.localScale = new Vector3(1, hitResult.distance / _spellEntity.prefabRange, 1);
                    }

                    _collider2D.enabled = false;
                }
            }
            return _target;
        }

        public void TrySetTarget(GameObject target, bool force = false)
        {
            if (!force && _target)
            {
                return;
            }

            _target = target;
        }
        

        private void ExecuteTraceMove()
        {
            TryGetTarget();
            if (_target)
            {
                var targetDirection = (_target.transform.position - transform.position).normalized;
                var angle = Vector3.Angle(Direction, targetDirection);
                var angleDelta = traceTargetAngleSpeed * Time.deltaTime;
                if (angle < angleDelta)
                {
                    Direction = targetDirection;
                }
                else
                {
                    var cross = Vector3.Cross(Direction, targetDirection).z;
                    if (cross < 0)
                    {
                        angleDelta = -angleDelta;
                    }
            
                    SetDirection(Quaternion.Euler(new Vector3(0, 0, angleDelta)) * Direction);
                }
            }
          
            _movement = Direction * (traceTargetSpeed / 10 * Time.deltaTime);
            if (_rigidBody2D)
            {
                _rigidBody2D.MovePosition(this.transform.position + _movement);
            }
        }

        protected override void Initialization()
        {
            base.Initialization();
            _target = null;
            TryGetTarget();
        }
        
        
        public override void Movement()
        {
            if (targetFindType == TargetFindType.OverlapNearest)
            {
                if (_target)
                {
                    _damageOnTouch.Colliding(_target);
                }
                else
                {
                    ///没目标直接回收
                    HitStageDeath(null);
                }
                return;
            } 
            
            if (CurrentTime > activeLifeTime)
            {
                ExecuteTraceMove();
            }
            else
            {
                base.Movement();
            }

        }
    }
}